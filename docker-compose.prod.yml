services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: agrizy-mysql-prod
    restart: unless-stopped
    command: --skip-ssl --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: agrizy_files_db
      MYSQL_USER: db_user
      MYSQL_PASSWORD: jeh<PERSON>viconZygdan
    # Port 3306 not exposed externally - only accessible within Docker network
    volumes:
      - mysql_data:/var/lib/mysql
      - ./api/create-schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./api/init-rbac.sql:/docker-entrypoint-initdb.d/02-rbac.sql
    networks:
      - agrizy-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Backend API Service
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: agrizy-api-prod
    # Port 3030 not exposed externally - only accessible within Docker network
    environment:
      - NODE_ENV=production
      - PORT=3030
      - DB_HOST=mysql
      - DB_USER=root
      - DB_PASSWORD=root_password
      - DB_NAME=agrizy_files_db
      - DB_PORT=3306
      - DB_SSL=false
      - CORS_ORIGIN=https://file.agrizy.in
      - JWT_SECRET=your-production-secret-key
    volumes:
      - api_data:/app/data
      - ~/uploads:/app/uploads
      - ./api/create-schema.sql:/app/create-schema.sql:ro
      - ./api/init-rbac.sql:/app/init-rbac.sql:ro
      - ./api/seed-database.sql:/app/seed-database.sql:ro
      - ./api/seed-database.js:/app/seed-database.js:ro
      - ./api/seed-db.sh:/app/seed-db.sh:ro
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - agrizy-network
    restart: unless-stopped
    # Start the main application (seeding will be handled by the app itself)
    command: >
      sh -c "mariadb -h mysql -u root -proot_password --skip-ssl --default-auth=mysql_native_password -e 'SELECT 1' &&
             npm start"

  # Frontend Web Service
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: agrizy-web-prod
    ports:
      - "3005:3000"  # Changed from 3000:3000 to 3005:3000
    environment:
      - VITE_API_BASE_URL=
      - VITE_API_PROXY_TARGET=http://api:3030
      - VITE_APP_TITLE=Agrizy File Browser
    depends_on:
      - api
    networks:
      - agrizy-network
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  api_data:
    driver: local

networks:
  agrizy-network:
    driver: bridge