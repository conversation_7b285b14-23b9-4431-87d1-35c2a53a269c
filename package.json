{"name": "agrizy-file-browser", "version": "1.0.0", "description": "Agrizy File Browser - Full Stack Application", "private": true, "type": "module", "scripts": {"dev": "./scripts/port-manager.sh start", "start": "./scripts/port-manager.sh start", "stop": "./scripts/port-manager.sh stop", "restart": "./scripts/port-manager.sh restart", "port:clean": "./scripts/port-manager.sh clean", "port:status": "./scripts/port-manager.sh status", "install:all": "cd api && npm install && cd ../web && npm install", "build:all": "cd api && npm run build && cd ../web && npm run build", "dev:frontend": "cd web && npm run dev", "dev:backend": "cd api && npm run dev", "setup": "npm run install:all && cd api && npm run migrate"}, "workspaces": ["api", "web"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "."}, "keywords": ["file-browser", "vue", "typescript", "fastify", "mysql"], "author": "Agrizy Development Team", "license": "UNLICENSED", "devDependencies": {"concurrently": "^8.2.2"}}