#!/bin/bash

# Deploy frontend fix for authentication redirect issue
echo "Deploying frontend authentication fix..."

# Create tarball of the dist directory
cd /Users/<USER>/home/<USER>/agrizy/file-browser/web
tar -czf dist.tar.gz dist/

# Transfer to server via SSM
echo "Transferring files to server..."
ssm-scp-put llm dist.tar.gz /tmp/dist.tar.gz

# Extract on server and replace old dist
echo "Extracting and deploying on server..."
ssm-connect llm "cd /home/<USER>/apps/file-browser/web && tar -xzf /tmp/dist.tar.gz && rm /tmp/dist.tar.gz"

echo "Frontend deployment complete!"
echo ""
echo "The authentication fix has been deployed. The changes:"
echo "1. Uses Vue Router navigation (router.push) instead of window.location.href"
echo "2. This avoids the race condition where the page reloads before localStorage is synced"
echo "3. Keeps the SPA context intact during navigation"
echo ""
echo "Please test the login flow at https://files.agrizy.in"