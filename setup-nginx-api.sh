#!/bin/bash

# Create the nginx configuration file
sudo tee /etc/nginx/sites-available/files-api.agrizy.in > /dev/null <<'EOF'
server {
    listen 80;
    server_name files-api.agrizy.in;
    
    location / {
        proxy_pass http://localhost:9090;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/files-api.agrizy.in /etc/nginx/sites-enabled/

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx

# Now run certbot for SSL
sudo certbot --nginx -d files-api.agrizy.in --non-interactive --agree-tos --email <EMAIL>

echo "Configuration complete!"