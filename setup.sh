#!/bin/bash

# Agrizy File Browser - Complete Setup Script
# This script sets up and starts the entire application with database seeding

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    log_success "Docker Compose is available"
}

# Function to clean up existing containers
cleanup_containers() {
    log_info "Cleaning up existing containers..."
    
    # Stop and remove existing containers
    docker-compose down --remove-orphans 2>/dev/null || true
    
    # Remove any dangling containers
    docker container prune -f >/dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

# Function to build and start services
start_services() {
    log_info "Building and starting services..."
    
    # Build and start all services
    if docker-compose up -d --build; then
        log_success "Services started successfully"
    else
        log_error "Failed to start services"
        exit 1
    fi
}

# Function to wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        # Check if web service is responding
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            log_success "Web service is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Services not ready yet, waiting 5 seconds..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    log_warning "Services may not be fully ready yet, but continuing..."
}

# Function to show service status
show_status() {
    log_info "Service Status:"
    docker-compose ps
    
    echo ""
    log_info "Service Logs (last 10 lines):"
    echo "=== MySQL Logs ==="
    docker-compose logs --tail=10 mysql
    
    echo ""
    echo "=== API Logs ==="
    docker-compose logs --tail=10 api
    
    echo ""
    echo "=== Web Logs ==="
    docker-compose logs --tail=10 web
}

# Function to display final information
show_final_info() {
    echo ""
    log_success "=== AGRIZY FILE BROWSER SETUP COMPLETE ==="
    echo ""
    log_info "🌐 Application URL: http://localhost:3000"
    log_info "🔐 Admin Access: Use Google <NAME_EMAIL>"
    log_info "📁 Default Source: Local Files (/app/data)"
    echo ""
    log_info "📋 Useful Commands:"
    log_info "  - View logs: docker-compose logs -f [service]"
    log_info "  - Stop services: docker-compose down"
    log_info "  - Restart services: docker-compose restart"
    log_info "  - View status: docker-compose ps"
    echo ""
    log_info "🔧 Troubleshooting:"
    log_info "  - If services fail to start, check: docker-compose logs"
    log_info "  - To reset database: docker-compose down -v && ./setup.sh"
    log_info "  - For port conflicts: lsof -ti:3000 | xargs kill -9"
    echo ""
}

# Main setup function
main() {
    echo ""
    log_info "🚀 Starting Agrizy File Browser Setup..."
    echo ""
    
    # Pre-flight checks
    check_docker
    check_docker_compose
    
    # Setup process
    cleanup_containers
    start_services
    wait_for_services
    
    # Show status and final info
    show_status
    show_final_info
    
    log_success "Setup completed successfully! 🎉"
}

# Script entry point
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Agrizy File Browser - Complete Setup Script"
    echo ""
    echo "This script will:"
    echo "  1. Check Docker and Docker Compose availability"
    echo "  2. Clean up any existing containers"
    echo "  3. Build and start all services (MySQL, API, Web)"
    echo "  4. Automatically seed the database with admin user and roles"
    echo "  5. Wait for services to be ready"
    echo "  6. Display access information"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --logs         Show service logs after setup"
    echo ""
    echo "Requirements:"
    echo "  - Docker and Docker Compose installed and running"
    echo "  - Ports 3000 available (web interface)"
    echo "  - Internet connection for building images"
    echo ""
    exit 0
fi

# Check for logs flag
if [ "$1" = "--logs" ]; then
    SHOW_LOGS=true
else
    SHOW_LOGS=false
fi

# Run main setup
main

# Show logs if requested
if [ "$SHOW_LOGS" = true ]; then
    echo ""
    log_info "📋 Showing detailed logs..."
    docker-compose logs
fi

exit 0