#!/bin/bash

# Run database seeding script
echo "Running database seeding script..."

mysql -h db01.agrizy.in -u db_user -pjeh<PERSON>viconZygdan agrizy_files_db << 'EOF'
-- Core Database Seed (simplified, without user_groups)

-- Insert basic permissions
INSERT IGNORE INTO permissions (id, name, description, category) VALUES
('admin.all', 'Full Admin Access', 'Complete administrative access to all features', 'admin'),
('sources.read', 'Read Sources', 'View file sources and browse files', 'sources'),
('sources.write', 'Write Sources', 'Create, update, and delete file sources', 'sources'),
('sources.admin', 'Admin Sources', 'Full administrative access to sources', 'sources'),
('files.read', 'Read Files', 'View and download files', 'files'),
('files.write', 'Write Files', 'Upload, modify, and delete files', 'files'),
('files.admin', 'Admin Files', 'Full administrative access to files', 'files'),
('users.read', 'Read Users', 'View user information', 'users'),
('users.write', 'Write Users', 'Create and modify users', 'users'),
('users.admin', 'Admin Users', 'Full administrative access to users', 'users'),
('audit.read', 'Read Audit Logs', 'View audit logs and system activity', 'audit'),
('system.admin', 'System Admin', 'System-level administrative access', 'system');

-- Insert basic roles
INSERT IGNORE INTO roles (id, name, description, is_builtin) VALUES
('admin', 'Administrator', 'Full system administrator with all permissions', TRUE),
('manager', 'Manager', 'Manager with read/write access to most features', TRUE),
('editor', 'Editor', 'Editor with file read/write access', TRUE),
('user', 'User', 'Standard user with basic file access', TRUE),
('viewer', 'Viewer', 'Read-only access to files and sources', TRUE);

-- Assign permissions to admin role
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
('admin', 'admin.all'),
('admin', 'sources.admin'),
('admin', 'files.admin'),
('admin', 'users.admin'),
('admin', 'audit.read'),
('admin', 'system.admin');

-- Assign permissions to manager role
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
('manager', 'sources.read'),
('manager', 'sources.write'),
('manager', 'files.read'),
('manager', 'files.write'),
('manager', 'users.read'),
('manager', 'audit.read');

-- Assign permissions to editor role
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
('editor', 'sources.read'),
('editor', 'files.read'),
('editor', 'files.write');

-- Assign permissions to user role
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
('user', 'sources.read'),
('user', 'files.read'),
('user', 'files.write');

-- Assign permissions to viewer role
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
('viewer', 'sources.read'),
('viewer', 'files.read');

-- Create default source
INSERT IGNORE INTO sources (id, kind, name, config) VALUES
(1, 'local', 'Local Files', JSON_OBJECT('path', '/app/data', 'readonly', false, 'description', 'Default local file storage'));

-- Check results
SELECT 'Roles Created:' as Status, COUNT(*) as Count FROM roles WHERE is_builtin = TRUE;
SELECT 'Permissions Created:' as Status, COUNT(*) as Count FROM permissions;
SELECT 'Role Permissions:' as Status, COUNT(*) as Count FROM role_permissions;
SELECT 'Default Source:' as Status, name FROM sources WHERE id = 1;

SELECT 'Database Seeding Complete!' as status;
EOF

echo "Seeding complete!"