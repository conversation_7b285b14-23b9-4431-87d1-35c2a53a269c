# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Agrizy File Browser - A Vue.js + TypeScript frontend with Node.js/Fastify backend for role-based file browsing with multiple provider support (Local Files, S3, FTP, SFTP).

## Key Architecture

### Backend (`/api`)
- **Framework**: Fastify with TypeScript, ESM modules
- **Database**: MySQL 8.0 with connection pooling (`mysql2/promise`)
- **Authentication**: JWT-based with Google OAuth support
- **RBAC System**: Hierarchical roles with granular permissions
- **File Providers**: Abstract `Provider` interface with implementations for Local Files, S3, FTP, SFTP

### Frontend (`/web`)
- **Framework**: Vue 3 with Composition API, TypeScript, Vite
- **State Management**: Pinia stores
- **Routing**: Vue Router 4
- **API Communication**: Custom `api.ts` client with JWT handling

### Database Schema
- Uses MySQL with role-based access control tables
- Initialization scripts: `create-schema.sql`, `init-rbac.sql`
- Default admin user: `<EMAIL>`

## Development Commands

### Docker Setup (Recommended)
```bash
# Automated setup with database seeding
./setup.sh

# Manual Docker commands
docker-compose up -d --build    # Start all services
docker-compose logs -f          # View logs
docker-compose down             # Stop services
docker-compose ps               # Check status

# Database access
docker-compose exec mysql mysql -u db_user -p agrizy_files_db
```

### Local Development
```bash
# Backend (API on port 3030)
cd api
pnpm install
pnpm run dev    # Uses tsx watch for hot reload

# Frontend (Web on port 3000)
cd web
pnpm install
pnpm run dev    # Vite dev server

# Build production
cd api && pnpm run build
cd web && pnpm run build
```

### Testing & Validation
```bash
# Type checking
cd api && tsc --noEmit
cd web && tsc --noEmit

# No linting/test scripts configured yet
```

## File Provider Architecture

All file operations go through the abstract `Provider` interface (`api/src/providers/Provider.ts`):
- **Local Files**: `fs.ts` - Serves files from `/app/data` directory
- **S3**: `s3.ts` - AWS S3 integration with presigned URLs
- **FTP/SFTP**: `ftp.ts`, `sftp.ts` - Remote file access

Provider selection happens in `api/src/providers/bootstrap.ts` based on source configuration.

## API Routes Structure

Main routes in `/api/src/routes/`:
- `auth.ts` - Authentication, Google OAuth, JWT handling
- `admin.ts` - User management, role assignment, RBAC operations
- `files.ts` - Core file operations (list, read, write, delete)
- `sources.ts` - File source management
- `multipart.ts` - Multipart upload handling
- `presign.ts` - S3 presigned URL generation
- `preview.ts` - File preview generation
- `search.ts` - File search functionality
- `stream.ts` - File streaming for downloads

## Frontend Key Components

- **State Management**: Pinia stores for auth, permissions, preferences
- **Permissions System**: `usePermissions.ts` composable for RBAC checks
- **File Operations**: `lib/files.ts` for upload/download logic
- **API Client**: `lib/api.ts` with automatic JWT refresh

## Security & Authentication

- JWT tokens stored in localStorage
- Google OAuth integration via `google-auth-library`
- RBAC with hierarchical roles: Super Admin > Admin > Manager > User > Viewer
- Policy engine in `api/src/utils/policy.ts` for permission checks
- Audit logging in `api/src/utils/audit.ts`

## Environment Variables

### Backend (.env)
- `DB_HOST`, `DB_USER`, `DB_PASSWORD`, `DB_NAME` - MySQL connection
- `JWT_SECRET` - Must be changed in production
- `CORS_ORIGIN` - Frontend URL for CORS

### Frontend (.env)
- `VITE_API_BASE_URL` - Backend API URL
- `VITE_APP_TITLE` - Application title

## Important Implementation Details

1. **Database Connection**: Uses MySQL connection pool with automatic retry
2. **File Uploads**: Supports both multipart and presigned URL strategies
3. **Authentication Flow**: JWT with refresh tokens, Google OAuth as primary
4. **RBAC Hierarchy**: Permissions cascade from higher roles
5. **Provider Pattern**: All file operations abstracted through Provider interface
6. **Error Handling**: Centralized error handling with proper HTTP status codes