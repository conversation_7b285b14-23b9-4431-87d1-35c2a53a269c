#!/bin/bash

# Agrizy File Browser - Port Management Script
# This script implements the port management protocol defined in project_rules.md

set -e

# Define mandatory ports
FRONTEND_PORT=3000
BACKEND_PORT=3030

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[PORT-MANAGER]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    if [ -n "$pids" ]; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to get process info for a port
get_port_info() {
    local port=$1
    lsof -i:$port 2>/dev/null || echo "No process found on port $port"
}

# Function to terminate processes on a port
terminate_port_processes() {
    local port=$1
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        print_warning "Found processes on port $port:"
        get_port_info $port
        
        print_status "Terminating processes on port $port..."
        echo $pids | xargs kill -9 2>/dev/null || true
        
        # Wait a moment and verify
        sleep 1
        if check_port $port; then
            print_error "Failed to free port $port. Manual intervention required."
            print_error "Run: kill -9 \$(lsof -ti:$port)"
            exit 1
        else
            print_success "Port $port freed successfully"
        fi
    else
        print_success "Port $port is already free"
    fi
}

# Function to verify all required ports are free
verify_ports_free() {
    local all_free=true
    
    for port in $FRONTEND_PORT $BACKEND_PORT; do
        if check_port $port; then
            print_error "Port $port is still in use after cleanup"
            get_port_info $port
            all_free=false
        fi
    done
    
    if [ "$all_free" = false ]; then
        print_error "Port verification failed. Cannot proceed with service startup."
        exit 1
    fi
}

# Function to clean all required ports
clean_ports() {
    print_status "Starting port cleanup protocol..."
    print_status "Required ports: Frontend($FRONTEND_PORT), Backend($BACKEND_PORT)"
    
    # Terminate processes on required ports
    terminate_port_processes $FRONTEND_PORT
    terminate_port_processes $BACKEND_PORT
    
    # Verify all ports are free
    verify_ports_free
    
    print_success "All required ports are now available"
}

# Function to check port status
status() {
    print_status "Checking port status..."
    
    for port in $FRONTEND_PORT $BACKEND_PORT; do
        if check_port $port; then
            print_warning "Port $port is IN USE:"
            get_port_info $port
        else
            print_success "Port $port is FREE"
        fi
    done
}

# Function to start services with port verification
start_services() {
    print_status "Starting services with port verification..."
    
    # Clean ports first
    clean_ports
    
    # Start backend
    print_status "Starting backend on port $BACKEND_PORT..."
    cd api
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # Wait a moment for backend to start
    sleep 3
    
    # Verify backend is running
    if ! check_port $BACKEND_PORT; then
        print_error "Backend failed to start on port $BACKEND_PORT"
        exit 1
    fi
    print_success "Backend started successfully on port $BACKEND_PORT"
    
    # Start frontend
    print_status "Starting frontend on port $FRONTEND_PORT..."
    cd web
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    # Wait a moment for frontend to start
    sleep 3
    
    # Verify frontend is running
    if ! check_port $FRONTEND_PORT; then
        print_error "Frontend failed to start on port $FRONTEND_PORT"
        # Kill backend if frontend fails
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    print_success "Frontend started successfully on port $FRONTEND_PORT"
    
    print_success "All services are running:"
    print_success "  - Backend: http://localhost:$BACKEND_PORT"
    print_success "  - Frontend: http://localhost:$FRONTEND_PORT"
    
    # Save PIDs for later cleanup
    echo $BACKEND_PID > .backend.pid
    echo $FRONTEND_PID > .frontend.pid
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    
    # Stop using saved PIDs if available
    if [ -f .backend.pid ]; then
        BACKEND_PID=$(cat .backend.pid)
        kill $BACKEND_PID 2>/dev/null || true
        rm .backend.pid
    fi
    
    if [ -f .frontend.pid ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        kill $FRONTEND_PID 2>/dev/null || true
        rm .frontend.pid
    fi
    
    # Clean ports as backup
    clean_ports
    
    print_success "Services stopped"
}

# Main script logic
case "${1:-}" in
    "clean")
        clean_ports
        ;;
    "status")
        status
        ;;
    "start")
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        stop_services
        sleep 2
        start_services
        ;;
    *)
        echo "Usage: $0 {clean|status|start|stop|restart}"
        echo ""
        echo "Commands:"
        echo "  clean   - Terminate all processes on required ports"
        echo "  status  - Check current port usage"
        echo "  start   - Clean ports and start all services"
        echo "  stop    - Stop all services and clean ports"
        echo "  restart - Stop and start all services"
        echo ""
        echo "Required Ports:"
        echo "  Frontend: $FRONTEND_PORT"
        echo "  Backend:  $BACKEND_PORT"
        exit 1
        ;;
esac