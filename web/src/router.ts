import { createRouter, createWebHistory } from 'vue-router'
import Home from './views/Home.vue'
import Upload from './views/Upload.vue'
import FileBrowser from './views/FileBrowser.vue'
import Login from './views/Login.vue'

import MainLayout from './layouts/MainLayout.vue'
import AdminSources from './views/admin/AdminSources.vue'
import AdminPolicies from './views/admin/AdminPolicies.vue'
import AdminUsers from './views/admin/AdminUsers.vue'
import AdminGroups from './views/admin/AdminGroups.vue'
import AdminRoles from './views/admin/AdminRoles.vue'
import AdminPermissions from './views/admin/AdminPermissions.vue'
import { getToken } from '@/lib/auth'

const routes = [
  { path: '/login', name: 'login', component: Login },
  { path: '/', component: MainLayout, children: [
      { path: '', name: 'home', component: Home },
      { path: 'browser', name: 'browser', component: File<PERSON>rowser },
      { path: 'upload', name: 'upload', component: Upload },

      { path: 'admin', children: [
        { path: '', redirect: '/admin/sources' },
        { path: 'sources', component: AdminSources },
        { path: 'policies', component: AdminPolicies },
        { path: 'users', component: AdminUsers },
        { path: 'groups', component: AdminGroups },
        { path: 'roles', component: AdminRoles },
        { path: 'permissions', component: AdminPermissions }
      ]}
    ]}
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to) => {
  const token = getToken()
  const isLoginPage = to.path === '/login'
  
  // Redirect to login if not authenticated and not on login page
  if (!isLoginPage && !token) {
    return '/login'
  }
  
  // Redirect to home if authenticated and trying to access login page
  if (isLoginPage && token) {
    return '/'
  }
})

export default router
