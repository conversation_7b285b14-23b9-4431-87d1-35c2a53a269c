import { reactive, watch } from 'vue'

const KEY = 'fb_prefs'

type Prefs = {
  foldersFirst: boolean
  hideHidden: boolean
  recentSources: { id: number, name: string, kind: string }[]
}

const defaults: Prefs = {
  foldersFirst: true,
  hideHidden: true,
  recentSources: [],
}

function load(): Prefs {
  try {
    const raw = localStorage.getItem(KEY)
    return raw ? { ...defaults, ...JSON.parse(raw) } : { ...defaults }
  } catch {
    return { ...defaults }
  }
}

const state = reactive(load())

watch(state, () => {
  localStorage.setItem(KEY, JSON.stringify(state))
})

export function usePrefs() {
  return state
}

export function addRecentSource(s: { id: number, name: string, kind: string }) {
  const list = state.recentSources || []
  const filtered = list.filter(it => it.id !== s.id)
  state.recentSources = [s, ...filtered].slice(0, 5)
}

export function removeRecentSource(sourceId: number) {
  const list = state.recentSources || []
  state.recentSources = list.filter(it => it.id !== sourceId)
}
