export type User = { sub: string; email?: string; name?: string; picture?: string; roles?: string[] }

const KEY = 'fb_auth_token'

export function getToken() {
  return localStorage.getItem(KEY) || ''
}

export function setToken(t: string) {
  localStorage.setItem(KEY, t)
}

export function clearToken() {
  localStorage.removeItem(KEY)
}

export async function loginWithGoogleIdToken(idToken: string) {
  const { apiRequest } = await import('./api')
  const res = await apiRequest('/v1/auth/google', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ idToken }) })
  if (!res.ok) {
    const errorData = await res.json().catch(() => ({}))
    const errorCode = errorData.error?.code
    const errorMessage = errorData.error?.message || 'Login failed'
    
    if (errorCode === 'INACTIVE') {
      throw new Error('Your account is not yet activated. Please contact an administrator to activate your account.')
    } else if (errorCode === 'FORBIDDEN_DOMAIN') {
      throw new Error('Your email domain is not allowed to access this application.')
    } else if (errorCode === 'BLOCKED') {
      throw new Error('Your account has been blocked. Please contact an administrator.')
    }
    
    throw new Error(errorMessage)
  }
  const data = await res.json() as { token: string, user: User }
  setToken(data.token)
  return data
}

export function authHeaders() {
  const t = getToken()
  return t ? { Authorization: `Bearer ${t}` } : {}
}
