// Use environment variable for API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''
const API = `${API_BASE_URL}/v1`

export async function uploadFileFS(file: File, destPath: string, overwrite = false, onProgress?: (sent: number, total: number) => void) {
  // Basic upload via fetch; progress optional using stream if available
  let body: BodyInit
  if (file.stream && typeof ReadableStream !== 'undefined') {
    const total = file.size
    const src = file.stream()
    const ts = new TransformStream<Uint8Array, Uint8Array>({
      start() {},
      async transform(chunk, controller) {
        controller.enqueue(chunk)
        onProgress?.(chunk.byteLength, total) // Note: increments by chunk; caller aggregates
      }
    })
    body = src.pipeThrough(ts) as any
  } else {
    body = file
  }

  const url = `${API}/files/upload?provider=fs&path=${encodeURIComponent(destPath)}&overwrite=${overwrite ? 'true' : 'false'}`
  const res = await fetch(url, { method: 'POST', body })
  if (!res.ok) throw new Error('FS upload failed')
  return res.json()
}

