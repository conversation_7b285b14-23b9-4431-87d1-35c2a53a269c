import { authHeaders } from './auth'
import { authenticatedApiRequest } from './api'

// For backward compatibility, still need API base for some non-authenticated calls
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''
const API = `${API_BASE_URL}/v1`

export type FileItem = {
  name: string
  path: string
  kind: 'file' | 'dir'
  size?: number
  modifiedAt?: string
  mime?: string
  allowedOps?: string[]
}

export async function listFiles(
  provider: 'fs'|'s3',
  path: string,
  page = 1,
  size = 200,
  sort?: 'name'|'size'|'modifiedAt',
  order?: 'asc'|'desc',
  hideHidden?: boolean,
  sourceId?: string
) {
  const params = new URLSearchParams({
    provider,
    path,
    page: String(page),
    size: String(size),
  })
  if (sort) params.set('sort', sort)
  if (order) params.set('order', order)
  if (hideHidden) params.set('hideHidden', String(hideHidden))
  if (sourceId) params.set('sourceId', sourceId)
  const url = `${API}/files?` + params.toString()
  const res = await fetch(url, { headers: { ...authHeaders() } as HeadersInit })
  if (!res.ok) throw new Error('Failed to list files')
  return res.json() as Promise<{ items: FileItem[], page: number, size: number, total: number }>
}

export async function mkdir(provider: 'fs'|'s3', path: string, sourceId?: string) {
  const res = await fetch(`${API}/files`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', ...authHeaders() } as HeadersInit,
    body: JSON.stringify({ provider, path, type: 'dir', sourceId })
  })
  if (!res.ok) throw new Error('Failed to create directory')
  return res.json()
}

export async function remove(provider: 'fs'|'s3', path: string, sourceId?: string) {
  const res = await fetch(`${API}/files`, {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json', ...authHeaders() } as HeadersInit,
    body: JSON.stringify({ provider, path, sourceId })
  })
  if (!res.ok) throw new Error('Failed to delete')
  return res.json()
}

export async function move(provider: 'fs'|'s3', src: string, dst: string, sourceId?: string) {
  const res = await fetch(`${API}/files/move`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', ...authHeaders() } as HeadersInit,
    body: JSON.stringify({ provider, src, dst, sourceId })
  })
  if (!res.ok) throw new Error('Failed to move')
  return res.json()
}

export async function copy(provider: 'fs'|'s3', src: string, dst: string, sourceId?: string) {
  const res = await fetch(`${API}/files/copy`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', ...authHeaders() } as HeadersInit,
    body: JSON.stringify({ provider, src, dst, sourceId })
  })
  if (!res.ok) throw new Error('Failed to copy')
  return res.json()
}

export async function presignDownload(provider: 's3', path: string, ttlSeconds = 900) {
  const res = await fetch(`${API}/downloads/presign`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', ...authHeaders() } as HeadersInit,
    body: JSON.stringify({ provider, path, ttlSeconds })
  })
  if (!res.ok) throw new Error('Failed to presign download')
  return res.json() as Promise<{ url: string }>
}

export async function searchFiles(
  provider: 'fs'|'s3',
  path: string,
  q: string,
  page = 1,
  size = 200,
  recursive = true,
  kind?: 'file'|'dir',
  sourceId?: string
) {
  const params = new URLSearchParams({
    provider,
    path,
    q,
    page: String(page),
    size: String(size),
    recursive: String(recursive),
  })
  if (kind) params.set('kind', kind)
  if (sourceId) params.set('sourceId', sourceId)
  const res = await fetch(`${API}/search?` + params.toString(), { headers: { ...authHeaders() } as HeadersInit })
  if (!res.ok) throw new Error('Search failed')
  return res.json() as Promise<{ items: FileItem[], page: number, size: number, total: number }>
}

export async function previewMeta(provider: 'fs'|'s3', path: string) {
  const params = new URLSearchParams({ provider, path })
  const res = await fetch(`${API}/previews/meta?` + params.toString(), { headers: { ...authHeaders() } as HeadersInit })
  if (!res.ok) throw new Error('Preview meta failed')
  return res.json() as Promise<{ path: string, mime?: string, kind: 'image'|'pdf'|'text'|'binary', available: boolean, strategies: string[] }>
}
