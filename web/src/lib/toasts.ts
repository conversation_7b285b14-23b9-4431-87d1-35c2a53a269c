import { reactive } from 'vue'

export type Toast = {
  id: number
  text: string
  kind?: 'info' | 'success' | 'error'
  actionLabel?: string
  action?: () => void | Promise<void>
  timeoutMs?: number
}

const state = reactive<{ items: Toast[]; nextId: number }>({ items: [], nextId: 1 })

export function useToasts() {
  function push(t: Omit<Toast, 'id'>) {
    const id = state.nextId++
    const toast: Toast = { id, timeoutMs: 4000, kind: 'info', ...t }
    state.items.push(toast)
    if (toast.timeoutMs && !toast.action) {
      setTimeout(() => dismiss(id), toast.timeoutMs)
    }
    return id
  }
  function dismiss(id: number) {
    const i = state.items.findIndex((x) => x.id === id)
    if (i >= 0) state.items.splice(i, 1)
  }
  return { state, push, dismiss }
}

