// API configuration - use environment variable or fallback to relative URLs
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''

console.log('API_BASE_URL from env:', import.meta.env.VITE_API_BASE_URL)
console.log('API_BASE_URL used:', API_BASE_URL)

/**
 * Make an API request with the correct base URL
 */
export async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<Response> {
  const url = `${API_BASE_URL}${endpoint}`
  console.log('Making request to:', url)
  return fetch(url, options)
}

/**
 * Make an authenticated API request
 */
export async function authenticatedApiRequest(endpoint: string, options: RequestInit = {}): Promise<Response> {
  const { authHeaders } = await import('./auth')
  const auth = authHeaders()
  const headers: Record<string, string> = {
    ...(options.headers as Record<string, string> || {}),
    ...(auth.Authorization ? { Authorization: auth.Authorization } : {})
  }
  
  return apiRequest(endpoint, {
    ...options,
    headers
  })
}