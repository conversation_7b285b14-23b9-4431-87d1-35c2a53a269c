export type PartETag = { ETag: string; PartNumber: number }

// Use environment variable for API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''
const API = `${API_BASE_URL}/v1`

export async function initMultipart(path: string, contentType?: string) {
  const res = await fetch(`${API}/uploads/multipart/init`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ provider: 's3', path, contentType })
  })
  if (!res.ok) throw new Error('init multipart failed')
  return res.json() as Promise<{ uploadId: string }>
}

export async function presignPart(path: string, uploadId: string, partNumber: number, ttlSeconds = 900) {
  const res = await fetch(`${API}/uploads/multipart/presignPart`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ provider: 's3', path, uploadId, partNumber, ttlSeconds })
  })
  if (!res.ok) throw new Error('presign part failed')
  return res.json() as Promise<{ url: string }>
}

export async function completeMultipart(path: string, uploadId: string, parts: PartETag[]) {
  const res = await fetch(`${API}/uploads/multipart/complete`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ provider: 's3', path, uploadId, parts })
  })
  if (!res.ok) throw new Error('complete failed')
  return res.json()
}

export async function abortMultipart(path: string, uploadId: string) {
  const res = await fetch(`${API}/uploads/multipart/abort`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ provider: 's3', path, uploadId })
  })
  if (!res.ok) throw new Error('abort failed')
  return res.json()
}

async function fetchWithRetry(url: string, init: RequestInit, attempts = 3, baseDelayMs = 300) {
  let last: Response | null = null
  for (let i = 0; i < attempts; i++) {
    try {
      const res = await fetch(url, init)
      if (res.ok) return res
      last = res
    } catch {
      // ignore; retry
    }
    await new Promise(r => setTimeout(r, baseDelayMs * Math.pow(2, i)))
  }
  if (last) return last
  throw new Error('network error')
}

export async function uploadFileMultipart(file: File, destPath: string, opts?: { partSizeMB?: number, maxRetries?: number }) {
  const partSize = Math.max(5, Math.min(128, opts?.partSizeMB ?? 5)) * 1024 * 1024
  const { uploadId } = await initMultipart(destPath, file.type)
  const parts: PartETag[] = []
  try {
    let partNumber = 1
    for (let offset = 0; offset < file.size; offset += partSize) {
      const chunk = file.slice(offset, Math.min(offset + partSize, file.size))
      const { url } = await presignPart(destPath, uploadId, partNumber)
      const put = await fetchWithRetry(url, { method: 'PUT', body: chunk }, opts?.maxRetries ?? 3)
      if (!put.ok) throw new Error(`upload part ${partNumber} failed`)
      const etag = put.headers.get('ETag') || ''
      parts.push({ ETag: etag.replaceAll('"',''), PartNumber: partNumber })
      partNumber++
    }
    await completeMultipart(destPath, uploadId, parts)
    return { ok: true }
  } catch (e) {
    await abortMultipart(destPath, uploadId).catch(() => {})
    throw e
  }
}
