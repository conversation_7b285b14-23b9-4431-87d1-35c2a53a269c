export function iconFor(kind: 'file' | 'dir', name: string, mime?: string) {
  if (kind === 'dir') return '📁'
  const low = name.toLowerCase()
  const m = (mime || '').toLowerCase()
  if (m.startsWith('image/') || /\.(png|jpe?g|gif|webp|svg)$/.test(low)) return '🖼️'
  if (m.startsWith('video/') || /\.(mp4|mkv|webm|mov)$/.test(low)) return '🎬'
  if (m.startsWith('audio/') || /\.(mp3|wav|ogg|m4a)$/.test(low)) return '🎵'
  if (m === 'application/pdf' || /\.pdf$/.test(low)) return '📄'
  if (/(zip|tar|gz|bz2|xz|7z)$/.test(low)) return '🗜️'
  if (/\.(js|ts|tsx|jsx|json|yml|yaml|xml|html|css|scss|md)$/.test(low)) return '🧩'
  if (/\.(txt|log)$/.test(low)) return '📄'
  if (/\.(csv|tsv|xlsx?)$/.test(low)) return '📊'
  if (/\.(pptx?|key)$/.test(low)) return '📽️'
  if (/\.(exe|bin|dll)$/.test(low)) return '💾'
  return '📄'
}

