import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'

export type SourceKind = 'fs' | 's3'

export type Source = {
  id: number
  name: string
  kind: SourceKind
}

export async function createSource(kind: SourceKind, name: string, config: any) {
  const res = await authenticatedApiRequest('/v1/sources', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ kind, name, config })
  })
  if (!res.ok) {
    const err = await res.json().catch(() => ({} as any))
    throw new Error(err?.error?.message || 'Create failed')
  }
  return await res.json()
}

export async function getAllSources(): Promise<Source[]> {
  const res = await authenticatedApiRequest('/v1/sources')
  if (!res.ok) {
    const err = await res.json().catch(() => ({} as any))
    throw new Error(err?.error?.message || 'Failed to fetch sources')
  }
  const data = await res.json()
  return data || []
}

