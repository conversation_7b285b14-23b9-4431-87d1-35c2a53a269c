<template>
  <div style="display:flex; gap:12px;">
    <ToastContainer />
    <aside style="width:240px; border:1px solid #d1d5db; border-radius:12px; background:#f3f4f6; padding:12px; height:100vh; position:sticky; top:0; display:flex; flex-direction:column; justify-content:space-between; box-shadow: 0 8px 24px rgba(0,0,0,0.08);">
      <div>
        <router-link class="nav" to="/" exact-active-class="router-link-exact-active">
          <Icon name="home" :size="16" style="margin-right: 8px;" />
          Dashboard
        </router-link>
        <!-- Recent sources (last 5), shown directly under Dashboard without a heading -->
        <div v-if="prefs.recentSources && prefs.recentSources.length" style="margin:4px 0 8px;">
          <router-link
            v-for="rs in prefs.recentSources.slice(0, 5)"
            :key="rs.id"
            class="nav"
            :to="{ path: '/browser', query: { provider: rs.kind, sourceId: rs.id } }"
            :class="{ 'router-link-exact-active': route.path === '/browser' && route.query.provider === rs.kind && route.query.sourceId === rs.id.toString() }"
            exact-active-class=""
            @click="handleSourceClick(rs)"
          >
            <Icon :name="rs.kind === 's3' ? 'cloud' : 'computer'" :size="16" style="margin-right: 8px;" />
            {{ rs.name }}
          </router-link>
        </div>


        <div v-if="isAdmin" style="margin-top:8px;">
          <div style="font-size:14px; font-weight:700; color:#111827; padding:0 12px 6px; display:flex; align-items:center;">
            <Icon name="admin" :size="14" style="margin-right: 6px;" />
            Administration
          </div>
          <router-link class="nav sub" to="/admin/sources">
            <Icon name="folder" :size="16" style="margin-right: 8px;" />
            Sources
          </router-link>
          <router-link class="nav sub" to="/admin/users">
            <Icon name="users" :size="16" style="margin-right: 8px;" />
            Users
          </router-link>
          <router-link class="nav sub" to="/admin/groups">
            <Icon name="group" :size="16" style="margin-right: 8px;" />
            Groups
          </router-link>
          <router-link class="nav sub" to="/admin/roles">
            <Icon name="shield" :size="16" style="margin-right: 8px;" />
            Roles
          </router-link>
          <router-link class="nav sub" to="/admin/permissions">
            <Icon name="shield" :size="16" style="margin-right: 8px;" />
            Permissions
          </router-link>
          <router-link class="nav sub" to="/admin/policies">
            <Icon name="lock" :size="16" style="margin-right: 8px;" />
            Policies
          </router-link>
        </div>
      </div>
      <div style="border-top:1px solid #e5e7eb; padding-top:10px; display:flex; justify-content:space-between; align-items:center;">
        <span style="color:#374151; font-size:13px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; max-width:150px;">{{ me?.name || me?.email || 'User' }}</span>
        <button @click="logout" style="padding:6px 8px; border:1px solid #e5e7eb; border-radius:6px;">Logout</button>
      </div>
    </aside>
    <section style="flex:1; min-width:0; padding:12px;">
      <router-view />
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { User } from '@/lib/auth'
import { authHeaders, clearToken } from '@/lib/auth'
import { usePrefs } from '@/lib/prefs'
import { usePermissions } from '@/composables/usePermissions'
import Icon from '@/components/Icon.vue'
import ToastContainer from '@/components/ToastContainer.vue'

const me = ref<User | null>(null)
const prefs = usePrefs()
const route = useRoute()
const router = useRouter()
const { isAdmin, fetchCurrentUser } = usePermissions()


async function loadMe(){
  const { authenticatedApiRequest } = await import('@/lib/api')
  const { clearToken } = await import('@/lib/auth')
  
  try {
    const r = await authenticatedApiRequest('/v1/auth/me')
    if (r.ok) {
      me.value = (await r.json()).user
      await fetchCurrentUser()
    } else {
      // Authentication failed, clear token and redirect to login
      console.log('Authentication failed, redirecting to login')
      clearToken()
      window.location.href = '/login'
    }
  } catch (error) {
    console.error('Error loading user:', error)
    clearToken()
    window.location.href = '/login'
  }
}

function handleSourceClick(rs: any) {
  console.log('Source clicked:', rs)
  console.log('Navigating to:', { path: '/browser', query: { provider: rs.kind, sourceId: rs.id } })
}



function logout(){ clearToken(); window.location.href='/login' }
onMounted(loadMe)
</script>

<style scoped>
.nav {
  display:flex; align-items:center; text-decoration:none; color:#111827; padding:10px 12px; border-radius:8px; border:1px solid transparent;
  white-space:nowrap; overflow:hidden; text-overflow:ellipsis;
}
.nav.sub { padding-left:24px; }
.nav.router-link-exact-active {
  background:#dbeafe; color:#0f172a; font-weight:700; border:1px solid #93c5fd;
}
.nav:hover { background:#eef2ff; }
:deep(.admin-table){ 
  width:100%; 
  border-collapse:separate; 
  border-spacing:0; 
  border: 2px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}
:deep(.admin-table thead tr){ 
  background:#dbeafe; 
  border-bottom: 2px solid #a3b3c7;
}
:deep(.admin-table thead th){ 
  background:#dbeafe; 
  border-bottom:1px solid #a3b3c7; 
  border-right:1px solid #a3b3c7; 
  padding:12px; 
  font-weight:600;
  text-align: left;
  color: #1e293b;
}
:deep(.admin-table tbody tr){ 
  transition: background-color 0.15s ease;
}
:deep(.admin-table tbody td){ 
  padding:12px; 
  border-bottom:1px solid #cbd5e1; 
  border-right:1px solid #cbd5e1;
  color: #374151;
}
:deep(.admin-table tbody tr:hover){ background:#f8fafc; }
</style>
