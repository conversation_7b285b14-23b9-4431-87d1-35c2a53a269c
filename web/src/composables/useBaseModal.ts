import { computed, ref, watch } from 'vue'
import type { Ref } from 'vue'

export interface BaseModalOptions<T> {
  initialData: () => T
  validate?: (data: T) => Record<string, string>
  onSubmit?: (data: T) => void | Promise<void>
  onCancel?: () => void
  onReset?: () => void
  onLoadData?: () => void
}

export interface BaseModalProps {
  modelValue: boolean
  mode: 'add' | 'edit'
}

export interface BaseModalEmits<T> {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: T): void
}

export function useBaseModal<T extends Record<string, any>>(
  props: BaseModalProps,
  emit: BaseModalEmits<T>,
  options: BaseModalOptions<T>
) {
  // Core modal state
  const show = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // Form state
  const formData = ref<T>(options.initialData()) as Ref<T>
  const errors = ref<Record<string, string>>({})
  const isSubmitting = ref(false)

  // Validation
  const validateForm = (): boolean => {
    if (options.validate) {
      errors.value = options.validate(formData.value)
      return Object.keys(errors.value).length === 0
    }
    return true
  }

  const isValid = computed(() => {
    if (options.validate) {
      const validationErrors = options.validate(formData.value)
      return Object.keys(validationErrors).length === 0
    }
    return true
  })

  // Core functions
  const reset = (): void => {
    formData.value = options.initialData()
    errors.value = {}
    isSubmitting.value = false
    if (options.onReset) {
      options.onReset()
    }
  }

  const loadData = (): void => {
    if (options.onLoadData) {
      options.onLoadData()
    }
  }

  const cancel = (): void => {
    reset()
    if (options.onCancel) {
      options.onCancel()
    }
    show.value = false
  }

  const submit = async (): Promise<void> => {
    if (!isValid.value || isSubmitting.value) return
    
    try {
      isSubmitting.value = true
      
      if (options.onSubmit) {
        await options.onSubmit(formData.value)
      }
      
      emit('submit', formData.value)
      reset()
      show.value = false
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      isSubmitting.value = false
    }
  }

  // Watch for modal state changes
  watch(show, (newValue) => {
    if (newValue) {
      loadData()
    } else {
      reset()
    }
  })

  return {
    // State
    show,
    formData,
    errors,
    isSubmitting,
    isValid,
    
    // Functions
    validateForm,
    reset,
    loadData,
    cancel,
    submit
  }
}