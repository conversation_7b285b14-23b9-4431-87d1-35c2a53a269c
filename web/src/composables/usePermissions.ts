import { ref, computed } from 'vue'
import { authHeaders } from '@/lib/auth'
import type { User } from '@/lib/auth'

export type Action = string
export type Role = 'admin' | 'editor' | 'viewer'

interface Policy {
  id: number
  subject_type: 'user' | 'group'
  subject_id: string
  source_id: number
  path_prefix: string
  action: string
  effect: 'allow' | 'deny'
  expires_at: string | null
}

interface PermissionCache {
  [key: string]: boolean
}

const currentUser = ref<User | null>(null)
const userPolicies = ref<Policy[]>([])
const userActions = ref<string[]>([])
const permissionCache = ref<PermissionCache>({})

export function usePermissions() {
  // Fetch current user
  const fetchCurrentUser = async () => {
    try {
      const { authenticatedApiRequest } = await import('@/lib/api')
      const res = await authenticatedApiRequest('/v1/auth/me')
      if (res.ok) {
        const data = await res.json() as { user: User }
        currentUser.value = data.user
        await fetchUserPolicies()
        await fetchUserActions()
      }
    } catch (error) {
      console.error('Failed to fetch current user:', error)
    }
  }

  // Fetch user policies
  const fetchUserPolicies = async () => {
    if (!currentUser.value) return
    
    try {
      const { authenticatedApiRequest } = await import('@/lib/api')
      const res = await authenticatedApiRequest(`/v1/policies/user/${currentUser.value.sub}`)
      if (res.ok) {
        userPolicies.value = await res.json()
        // Clear cache when policies change
        permissionCache.value = {}
      }
    } catch (error) {
      console.error('Failed to fetch user policies:', error)
    }
  }

  // Fetch user actions based on their roles
  const fetchUserActions = async () => {
    if (!currentUser.value) return
    
    try {
      const { authenticatedApiRequest } = await import('@/lib/api')
      const res = await authenticatedApiRequest(`/v1/auth/user-actions`)
      if (res.ok) {
        userActions.value = await res.json()
        // Clear cache when actions change
        permissionCache.value = {}
      }
    } catch (error) {
      console.error('Failed to fetch user actions:', error)
      // Fallback: if user has admin role, assume all actions
      if (currentUser.value.roles?.includes('admin')) {
        userActions.value = ['list', 'stat', 'read', 'write', 'move', 'copy', 'delete', 'share', 'zip', 'search']
      }
    }
  }

  // Check if user can perform action
  const canPerform = (action: Action, sourceId: number, path: string = '/'): boolean => {
    if (!currentUser.value) return false
    
    // Create cache key
    const cacheKey = `${action}:${sourceId}:${path}`
    if (permissionCache.value[cacheKey] !== undefined) {
      return permissionCache.value[cacheKey]
    }
    
    // Check if user has this action through their roles
    let allowed = userActions.value.includes(action)
    
    // Apply specific policies
    const relevantPolicies = userPolicies.value.filter(policy => {
      // Check if policy applies to this source
      if (policy.source_id !== sourceId) return false
      
      // Check if policy applies to this path
      const normalizedPath = path.endsWith('/') ? path : path + '/'
      const normalizedPrefix = policy.path_prefix.endsWith('/') ? policy.path_prefix : policy.path_prefix + '/'
      
      return normalizedPath.startsWith(normalizedPrefix)
    })
    
    // Apply policies in order (most specific first)
    relevantPolicies
      .sort((a, b) => b.path_prefix.length - a.path_prefix.length)
      .forEach(policy => {
        if (policy.action === action || policy.action === '*') {
          // Check if policy is expired
          if (policy.expires_at && new Date(policy.expires_at) < new Date()) {
            return
          }
          
          allowed = policy.effect === 'allow'
        }
      })
    
    // Cache result
    permissionCache.value[cacheKey] = allowed
    return allowed
  }

  // Get available actions for a path
  const getAvailableActions = (sourceId: number, path: string = '/'): string[] => {
    const allActions = ['view', 'list', 'stat', 'read', 'download', 'upload', 'write', 'move', 'copy', 'delete', 'share', 'zip', 'search']
    return allActions.filter(action => canPerform(action, sourceId, path))
  }

  // Check if user can perform any of the given actions
  const canPerformAny = (actions: Action[], sourceId: number, path: string = '/'): boolean => {
    return actions.some(action => canPerform(action, sourceId, path))
  }

  // Check if user can perform all of the given actions
  const canPerformAll = (actions: Action[], sourceId: number, path: string = '/'): boolean => {
    return actions.every(action => canPerform(action, sourceId, path))
  }

  // Clear permission cache
  const clearCache = () => {
    permissionCache.value = {}
  }

  // Computed properties
  const isAdmin = computed(() => currentUser.value?.roles?.includes('admin') || false)
  const userRole = computed(() => {
    const roles = currentUser.value?.roles || []
    if (roles.includes('admin')) return 'admin'
    if (roles.includes('editor')) return 'editor'
    return 'viewer'
  })

  return {
    currentUser: computed(() => currentUser.value),
    userPolicies: computed(() => userPolicies.value),
    userActions: computed(() => userActions.value),
    isAdmin,
    userRole,
    canPerform,
    canPerformAny,
    canPerformAll,
    getAvailableActions,
    fetchCurrentUser,
    fetchUserPolicies,
    fetchUserActions,
    clearCache
  }
}