export interface BaseModalProps {
  modelValue: boolean
}

export interface BaseModalEmits {
  (e: 'update:modelValue', value: boolean): void
}

// User-related types
export interface User {
  sub: string
  email: string
  name?: string
  blocked: boolean
  roles?: string[]
  is_admin?: boolean
  sources?: Source[]
}

export interface UserModalProps extends BaseModalProps {
  mode: 'add' | 'edit'
  user?: User
}

export interface UserModalEmits extends BaseModalEmits {
  (e: 'submit', data: UserFormData): void
}

export interface UserFormData {
  email: string
  name: string
  roles: string[]
}

// Group-related types
export interface Group {
  id: number
  name: string
  members?: string[]
}

export interface GroupModalProps extends BaseModalProps {
  mode: 'add' | 'edit'
  group?: Group
  users?: User[]
}

export interface GroupModalEmits extends BaseModalEmits {
  (e: 'submit', data: GroupFormData): void
}

export interface GroupFormData {
  name: string
  members?: string[]
}

// Role-related types
export interface Role {
  id: number
  name: string
  description?: string
  permissions?: string[]
  default_actions?: string | string[] | Record<string, any>
  user_count?: number
  created_at?: string
  is_builtin?: number
}

export interface RoleModalProps extends BaseModalProps {
  mode: 'add' | 'edit'
  role?: Role
}

export interface RoleModalEmits extends BaseModalEmits {
  (e: 'submit', data: RoleFormData): void
}

// Action types
export interface Permission {
  id: string
  name: string
  description?: string
  resource_type: string
  created_at?: string
  updated_at?: string
}

export interface PermissionModalProps extends BaseModalProps {
  mode: 'add' | 'edit'
  permission?: Permission
}

export interface PermissionFormData {
  name: string
  description: string
  resource_type: string
}

export interface PermissionModalEmits extends BaseModalEmits {
  (e: 'submit', data: PermissionFormData): void
}

export interface UserRoleModalProps extends BaseModalProps {
  user?: User
}

export interface UserRoleModalEmits extends BaseModalEmits {
  (e: 'submit', data: UserRoleFormData): void
}

export interface UserRoleFormData {
  roles: Role[]
}

export interface RoleFormData {
  name: string
  description: string
  permissions: string[]
}

// Source-related types
export interface Source {
  id: number
  name: string
  kind: 'fs' | 's3'
  config: string | FilesystemConfig | S3Config
}

export interface FilesystemConfig {
  root: string
}

export interface S3Config {
  bucket: string
  region: string
  endpoint?: string
  accessKeyId?: string
  secretAccessKey?: string
  rootPrefix?: string
  forcePathStyle?: boolean
}

export interface SourceModalProps extends BaseModalProps {
  mode: 'add' | 'edit'
  source?: Source
  showBrowseButton?: boolean
}

export interface SourceModalEmits extends BaseModalEmits {
  (e: 'submit', data: SourceFormData): void
  (e: 'browse'): void
}

export interface SourceFormData {
  kind: '' | 'fs' | 's3'
  name: string
  config: FilesystemConfig | S3Config
}

// Validation types
export interface ValidationErrors {
  [key: string]: string
}

export interface FormValidation {
  errors: ValidationErrors
  isValid: boolean
}

// Common modal styling constants
export const MODAL_STYLES = {
  container: 'display:flex; flex-direction:column; gap:16px;',
  fieldGroup: 'display:flex; flex-direction:column; gap:8px;',
  label: 'font-weight:600; color:#374151; font-size:14px;',
  input: 'padding:8px; border:1px solid #e5e7eb; border-radius:6px; font-size:14px;',
  select: 'padding:8px; border:1px solid #e5e7eb; border-radius:6px; font-size:14px;',
  textarea: 'padding:8px; border:1px solid #e5e7eb; border-radius:6px; font-size:14px; resize:vertical; min-height:80px;',
  error: 'color:#dc2626; font-size:12px; margin-top:4px;',
  grid: 'display:grid; grid-template-columns:1fr 1fr; gap:8px;',
  gridFull: 'grid-column:1/-1;',
  footer: 'display:flex; gap:8px; justify-content:flex-end;',
  buttonCancel: 'padding:8px 16px; border:1px solid #e5e7eb; border-radius:6px; background:#fff; cursor:pointer; font-size:14px;',
  buttonPrimary: 'padding:8px 16px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff; cursor:pointer; font-size:14px;',
  buttonDanger: 'padding:8px 16px; border:1px solid #dc2626; border-radius:6px; background:#dc2626; color:#fff; cursor:pointer; font-size:14px;',
  checkbox: 'display:flex; align-items:center; gap:6px; font-size:14px; color:#4b5563;'
} as const