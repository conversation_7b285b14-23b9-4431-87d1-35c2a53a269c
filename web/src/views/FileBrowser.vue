<template>
  <section>
    <div class="toolbar" style="display:flex; align-items:center; gap:12px; margin:0 0 8px 0; width:100%; background:#e5e7eb; color:#111827; padding:10px 12px; border-radius:8px; border:1px solid #cbd5e1;">
      <h1 style="margin:0; font-size:18px; display:flex; align-items:center; gap:10px;">
        <button @click="router.push('/')" title="Back" style="border:1px solid #cbd5e1; background:#fff; color:#111827; border-radius:6px; padding:4px 8px; cursor:pointer;">←</button>
        File Browser - <span class="crumbs-inline"><Breadcrumbs :path="currentPath" @navigate="navigate" /></span>
      </h1>
      <div style="flex:1;"></div>
      <button class="icon-btn" @click="goHome" :style="iconBtn()" title="Home"><Icon name="home" :size="20" :color="iconColor('light')"/></button>
      <button class="icon-btn" @click="router.push('/')" :style="iconBtn()" title="Close">✖️</button>
      <span :style="divider"></span>

      <button class="icon-btn" @click="goUp" :style="iconBtn()" title="Up"><Icon name="up" :size="20" :color="iconColor('light')"/></button>
      <button class="icon-btn" @click="refresh" :style="iconBtn()" title="Refresh"><Icon name="refresh" :size="20" :color="iconColor('light')"/></button>
      <span :style="divider"></span>
      <button class="icon-btn" @click="promptNewFolder" :style="iconBtn()" :disabled="!canPerform('write', parseInt(sourceId) || 1, currentPath)" title="New Folder"><Icon name="folder" :size="20" :color="iconColor('light')"/></button>
      <button class="icon-btn" @click="triggerUpload" :style="iconBtn()" :disabled="!canPerform('upload', parseInt(sourceId) || 1, currentPath)" title="Upload"><Icon name="upload" :size="20" :color="iconColor('light')"/></button>
      <button class="icon-btn" @click="renameSelected" :style="iconBtn()" :disabled="selected.size !== 1 || busy || !canPerform('move', parseInt(sourceId) || 1, currentPath)" title="Rename"><Icon name="edit" :size="20" :color="iconColor('light')"/></button>
      <button class="icon-btn" @click="moveSelected" :style="iconBtn()" :disabled="selected.size === 0 || busy || !canPerform('move', parseInt(sourceId) || 1, currentPath)" title="Move"><Icon name="move" :size="20" :color="iconColor('light')"/></button>
      <button class="icon-btn" @click="deleteSelected" :style="iconBtn('danger')" :disabled="selected.size === 0 || busy || !canPerform('delete', parseInt(sourceId) || 1, currentPath)" title="Trash"><Icon name="trash" :size="20" :color="iconColor('danger')"/></button>
      <span :style="divider"></span>
      <button class="icon-btn" @click="selectAllVisible" :style="iconBtn('light')" title="Select All"><Icon name="check-square" :size="20" :color="iconColor('light')"/></button>
      <button class="icon-btn" @click="clearSelection" :style="iconBtn('danger')" title="Clear Selection"><Icon name="x-square" :size="20" :color="iconColor('danger')"/></button>
      <input ref="filePicker" type="file" @change="onPick" style="display:none;" />
      <span :style="divider"></span>
      <button class="icon-btn" @click="toggleFoldersFirst" :style="iconBtn(foldersFirst ? 'active' : 'light')" title="Folders First"><Icon name="sort" :size="20" :color="iconColor(foldersFirst ? 'active' : 'light')"/></button>
      <div class="search-box" style="display:flex; align-items:center; gap:6px; background:#ffffff; border:1px solid #d1d5db; padding:4px 8px; border-radius:6px; color:#111827;">
        <Icon name="search" :size="20" color="#111827"/>
        <input v-model="searchQuery" @keydown.enter.prevent="runSearch" placeholder="Search" style="background:transparent; color:#111827; border:0; outline:none; min-width:220px;" />
        <button @click="runSearch" :style="iconBtn('light')" title="Search"><Icon name="search" :size="20" color="#111827"/></button>
        <button v-if="searchQuery" @click="clearSearch" :style="iconBtn('danger')" title="Clear"><Icon name="x" :size="20" color="#b91c1c"/></button>
      </div>
    </div>
    

    <div style="min-height:60vh;">
    <table class="file-table" style="width:100%; border-collapse:collapse;">
      <thead>
        <tr style="text-align:left; border-bottom:1px solid #eee;">
          <th style="padding:0.5rem; width:2rem;"><input type="checkbox" :checked="allSelected" @change="toggleAll($event)"/></th>
          <th style="padding:0.5rem; cursor:pointer;" @click="toggleSort('name')">Name {{ sortIndicator('name') }}</th>
          <th style="padding:0.5rem;">Type</th>
          <th style="padding:0.5rem; cursor:pointer;" @click="toggleSort('size')">Size {{ sortIndicator('size') }}</th>
          <th style="padding:0.5rem; cursor:pointer;" @click="toggleSort('modifiedAt')">Modified {{ sortIndicator('modifiedAt') }}</th>
          <th style="padding:0.5rem;">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="it in items" :key="it.path" class="table-row" :class="{ selected: isSelected(it) }" style="border-bottom:1px solid #f5f5f5;" @click="rowClick(it, $event)" @dblclick="rowDblClick(it)">
          <td style="padding:0.5rem;"><input type="checkbox" :checked="isSelected(it)" @change.stop="checkboxToggle(it, $event)"/></td>
          <td style="padding:0.5rem;">
            <span style="margin-right:0.35rem;">{{ icon(it) }}</span>
            <template v-if="editingId===fullPath(it)">
              <input
                v-model="tempName"
                @keydown.enter.prevent="commitRename(it)"
                @keydown.esc.prevent="cancelRename()"
                @blur="commitRename(it)"
                style="padding:2px 4px; border:1px solid #cbd5e1; border-radius:4px;"
                autofocus
              />
            </template>
            <template v-else>
              <a v-if="it.kind==='dir'" class="file-link" href="#" @click.stop.prevent="enter(it.name)">{{ it.name }}</a>
              <span v-else @click.stop="beginRename(it)" style="cursor:text;">{{ it.name }}</span>
            </template>
          </td>
          <td style="padding:0.5rem;">{{ it.kind }}</td>
          <td style="padding:0.5rem;">{{ formatSize(it.size) }}</td>
          <td style="padding:0.5rem;">{{ it.modifiedAt ? new Date(it.modifiedAt).toLocaleString() : '-' }}</td>
          <td style="padding:0.5rem; display:flex; gap:0.35rem;">
            <button class="row-icon-btn" :disabled="!can(it,'download')" v-if="it.kind==='file'" @click.stop="download(it)" :style="iconMini()" title="Download"><Icon name="download" :size="18" :color="'#334155'"/></button>
            <button class="row-icon-btn" :disabled="!can(it,'download')" v-else @click.stop="downloadZip(it)" :style="iconMini()" title="Download Zip"><Icon name="download" :size="18" :color="'#334155'"/></button>
            <button class="row-icon-btn" :disabled="!can(it,'upload')" @click.stop="softDeleteItem(it)" :style="iconMini('danger')" title="Trash"><Icon name="trash" :size="18" :color="'#b91c1c'"/></button>
            <button class="row-icon-btn" :disabled="!can(it,'view')" @click.stop="showPreview(it)" :style="iconMini()" title="Preview"><Icon name="eye" :size="18" :color="'#334155'"/></button>
          </td>
        </tr>
      </tbody>
    </table>
    </div>

    <p v-if="message" style="margin-top:0.5rem; color:#006400;">{{ message }}</p>
    <p v-if="error" style="margin-top:0.5rem; color:#8b0000;">{{ error }}</p>
    <ToastContainer />
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { listFiles, mkdir, presignDownload, move, searchFiles, previewMeta, type FileItem } from '@/lib/files'
import { uploadFileFS } from '@/lib/fsUpload'
import { uploadFileMultipart } from '@/lib/multipart'
import Breadcrumbs from '@/components/Breadcrumbs.vue'
import Icon from '@/components/Icon.vue'
import { iconFor } from '@/lib/icons'
import ToastContainer from '@/components/ToastContainer.vue'
import { useToasts } from '@/lib/toasts'
import { authHeaders } from '@/lib/auth'
import { usePrefs, removeRecentSource } from '@/lib/prefs'
import { getToken, clearToken } from '@/lib/auth'
import type { User } from '@/lib/auth'
import { usePermissions } from '@/composables/usePermissions'


const route = useRoute()
const router = useRouter()
const provider = ref<'fs'|'s3'>((route.query.provider as any) || 'fs')
const sourceId = ref<string>((route.query.sourceId as any) || '')

const prefs = usePrefs()
const currentPath = ref<'/'|string>('/')
const items = ref<FileItem[]>([])
const selected = ref<Set<string>>(new Set())
const busy = ref(false)
const message = ref('')
const error = ref('')
const editingId = ref<string | null>(null)
const tempName = ref('')
const toasts = useToasts()
const searchQuery = ref('')

const sortField = ref<'name'|'size'|'modifiedAt'>('name')
const sortOrder = ref<'asc'|'desc'>('asc')
const filePicker = ref<HTMLInputElement | null>(null)
const lastSelectedIndex = ref<number | null>(null)
const foldersFirst = ref<boolean>(prefs.foldersFirst)
const { currentUser, canPerform, fetchCurrentUser } = usePermissions()

function formatSize(n?: number) {
  if (n === undefined || n === null || isNaN(n as any)) return '-'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let v = n
  let i = 0
  while (v >= 1024 && i < units.length - 1) { v /= 1024; i++ }
  const num = v < 10 && i > 0 ? v.toFixed(1) : Math.round(v).toString()
  return `${num} ${units[i]}`
}

const titleText = computed(() => {
  const segs = currentPath.value.split('/').filter(Boolean)
  const name = segs.length ? segs[segs.length - 1] : 'Home'
  return `File Browser - ${name}`
})

function joinPath(base: string, name: string) {
  const b = base.endsWith('/') ? base.slice(0, -1) : base
  return (b === '' ? '/' : b + '/') + name
}

function parentDir(p: string) {
  const parts = p.split('/').filter(Boolean)
  parts.pop()
  return '/' + parts.join('/')
}

function btn(variant: 'primary'|'danger' = 'primary') {
  const base = 'padding:6px 10px; border-radius:6px; border:1px solid; font-size:12px; cursor:pointer;'
  if (variant === 'danger') return base + ' background:#7f1d1d; color:#fee2e2; border-color:#7f1d1d;'
  return base + ' background:#1e3a8a; color:#e0e7ff; border-color:#1e40af;'
}
const divider = 'width:1px; height:22px; background:#d1d5db; opacity:1; display:inline-block;'
function iconBtn(state: 'active'|'idle'|'danger'|'light' = 'idle') {
  const base = 'padding:6px 8px; border-radius:6px; border:1px solid; font-size:16px; cursor:pointer; background:#ffffff;'
  if (state === 'danger') return base + ' color:#b91c1c; border-color:#e5e7eb;'
  if (state === 'active') return base + ' color:#1e3a8a; border-color:#cbd5e1;'
  if (state === 'light') return base + ' color:#111827; border-color:#cbd5e1;'
  return base + ' color:#111827; border-color:#cbd5e1;'
}
function iconColor(state: 'active'|'idle'|'danger'|'light' = 'idle') {
  if (state === 'danger') return '#b91c1c'
  if (state === 'active') return '#1e3a8a'
  if (state === 'light') return '#111827'
  return '#111827'
}
function iconMini(variant: 'primary'|'danger' = 'primary') {
  const base = 'padding:2px 6px; border-radius:6px; border:1px solid; font-size:14px; cursor:pointer; background:#ffffff;'
  if (variant === 'danger') return base + ' color:#7f1d1d; border-color:#fca5a5;'
  return base + ' color:#334155; border-color:#e5e7eb;'
}

async function refresh() {
  try {
    console.log('Refreshing with provider:', provider.value, 'sourceId:', sourceId.value, 'path:', currentPath.value)
    message.value = ''
    error.value = ''
    const res = searchQuery.value
      ? await searchFiles(provider.value, currentPath.value, searchQuery.value, 1, 200, true, undefined, sourceId.value)
      : await listFiles(provider.value, currentPath.value, 1, 200, sortField.value, sortOrder.value, prefs.hideHidden, sourceId.value)
    // show dirs first then files
    items.value = sortItems(res.items)
    // Keep selection only for visible items to avoid stale highlights
    const visible = new Set(items.value.map(it => it.path))
    selected.value = new Set([...selected.value].filter(p => visible.has(p)))
  } catch (e: any) {
    error.value = e?.message || 'Failed to list'
    // If source not found, remove it from recent sources and redirect to home
    if (e?.message?.includes('Source not found') && sourceId.value) {
      removeRecentSource(parseInt(sourceId.value))
      router.push('/')
      return
    }
  }
}

function goUp() {
  if (currentPath.value === '/' ) return
  const parts = currentPath.value.split('/').filter(Boolean)
  parts.pop()
  currentPath.value = '/' + parts.join('/')
  if (currentPath.value === '//') currentPath.value = '/'
  refresh()
}

function enter(name: string) {
  currentPath.value = joinPath(currentPath.value, name)
  refresh()
}

function navigate(path: string) {
  currentPath.value = path
  refresh()
}

function setProvider(p: 'fs'|'s3') {
  provider.value = p
  // Clear sourceId when switching providers to let backend use default
  sourceId.value = ''
  console.log('Switching to provider:', p, 'cleared sourceId')
  const newQuery: any = { ...route.query, provider: p }
  delete newQuery.sourceId
  router.replace({ query: newQuery })
  refresh()
}

async function runSearch() {
  await refresh()
}

function clearSearch() {
  searchQuery.value = ''
  refresh()
}

function rowClick(it: FileItem, ev: MouseEvent) {
  const el = ev.target as HTMLElement
  const interactive = el.closest('button, a, input, svg, path')
  if (interactive) return
  const idx = items.value.findIndex(x => x.path === it.path)
  if (ev.shiftKey && lastSelectedIndex.value !== null) {
    const start = Math.min(lastSelectedIndex.value, idx)
    const end = Math.max(lastSelectedIndex.value, idx)
    for (let i = start; i <= end; i++) selected.value.add(items.value[i].path)
    lastSelectedIndex.value = idx
  } else {
    toggle(it)
  }
}

function sortItems(arr: FileItem[]) {
  const field = sortField.value
  const order = sortOrder.value
  const copy = [...arr]
  copy.sort((a: any, b: any) => {
    // For name sort, keep dirs first only if setting enabled
    if (field === 'name' && foldersFirst.value) {
      if (a.kind !== b.kind) return a.kind === 'dir' ? -1 : 1
    }
    let va: any = a[field]
    let vb: any = b[field]
    if (field === 'size') {
      va = typeof va === 'number' ? va : -1
      vb = typeof vb === 'number' ? vb : -1
    } else if (field === 'modifiedAt') {
      va = va ? Date.parse(va) : 0
      vb = vb ? Date.parse(vb) : 0
    } else {
      va = va ?? ''
      vb = vb ?? ''
    }
    const cmp = va < vb ? -1 : va > vb ? 1 : 0
    return order === 'asc' ? cmp : -cmp
  })
  return copy
}

function toggleSort(field: 'name'|'size'|'modifiedAt') {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'asc'
  }
  refresh()
}

// fetchCurrentUser is now provided by usePermissions composable

function logout() {
  clearToken()
  window.location.href = '/login'
}

function sortIndicator(field: 'name'|'size'|'modifiedAt') {
  if (sortField.value !== field) return ''
  return sortOrder.value === 'asc' ? '▲' : '▼'
}

function goHome() {
  currentPath.value = '/'
  refresh()
}

function promptNewFolder() {
  const name = prompt('New folder name:') || ''
  if (!name) return
  createDirWithName(name)
}

async function createDirWithName(name: string) {
  busy.value = true
  try {
    await mkdir(provider.value, joinPath(currentPath.value, name), sourceId.value)
    message.value = 'Folder created'
    refresh()
  } catch (e: any) {
    error.value = e?.message || 'Create folder failed'
  } finally {
    busy.value = false
  }
}

function triggerUpload() {
  filePicker.value?.click()
}

function beginRename(it: FileItem) {
  editingId.value = fullPath(it)
  tempName.value = it.name
}

async function commitRename(it: FileItem) {
  if (editingId.value !== fullPath(it)) return
  const newName = (tempName.value || '').trim()
  editingId.value = null
  if (!newName || newName === it.name) return
  busy.value = true
  try {
    const dst = joinPath(parentDir(fullPath(it)), newName)
    await move(provider.value, fullPath(it), dst, sourceId.value)
    toasts.push({ kind: 'success', text: `Renamed to ${newName}` })
    refresh()
  } catch (e: any) {
    error.value = e?.message || 'Rename failed'
    toasts.push({ kind: 'error', text: 'Rename failed' })
  } finally {
    busy.value = false
  }
}

function cancelRename() {
  editingId.value = null
}

function onPick(e: Event) {
  const input = e.target as HTMLInputElement
  const file = input.files && input.files[0] ? input.files[0] : null
  if (!file) return
  ;(async () => {
    busy.value = true
    try {
      const dest = joinPath(currentPath.value, file.name)
      if (provider.value === 'fs') {
        await uploadFileFS(file, dest, false)
      } else {
        await uploadFileMultipart(file, dest)
      }
      message.value = 'Upload complete'
      refresh()
    } catch (e: any) {
      error.value = e?.message || 'Upload failed'
    } finally {
      busy.value = false
      input.value = ''
    }
  })()
}

async function download(it: FileItem) {
  try {
    if (provider.value === 'fs') {
      const url = `/v1/files/download?provider=fs&path=${encodeURIComponent(it.path)}`
      window.open(url, '_blank')
    } else {
      const { url } = await presignDownload('s3', it.path)
      window.open(url, '_blank')
    }
  } catch (e: any) {
    error.value = e?.message || 'Download failed'
  }
}

function downloadZip(it: FileItem) {
  const t = localStorage.getItem('fb_auth_token') || ''
  const url = `/v1/files/zipdownload?provider=${encodeURIComponent(provider.value)}&path=${encodeURIComponent(it.path)}&token=${encodeURIComponent(t)}`
  window.open(url, '_blank')
}

function icon(it: FileItem) {
  return iconFor(it.kind, it.name, it.mime)
}

function can(it: FileItem, op: 'view'|'download'|'upload') {
  // Legacy function - keep existing behavior for now
  return (it.allowedOps || []).includes(op)
}

function fullPath(it: FileItem) {
  // Use server-provided path for consistency (also works with search results)
  return it.path
}

function isSelected(it: FileItem) {
  return selected.value.has(it.path)
}

function toggle(it: FileItem) {
  const p = it.path
  if (selected.value.has(p)) selected.value.delete(p)
  else selected.value.add(p)
}

function checkboxToggle(it: FileItem, event: Event) {
  event.stopPropagation()
  toggle(it)
}

const allSelected = computed(() => items.value.length > 0 && items.value.every(it => selected.value.has(it.path)))

function toggleAll(e: Event) {
  const checked = (e.target as HTMLInputElement).checked
  if (checked) {
    items.value.forEach(it => selected.value.add(it.path))
  } else {
    items.value.forEach(it => selected.value.delete(it.path))
  }
}

async function deleteSelected() {
  if (selected.value.size === 0) return
  await softDeleteMany(Array.from(selected.value))
}

async function moveSelected() {
  if (selected.value.size === 0) return
  const dstRoot = prompt('Move selected to path:', currentPath.value) || ''
  if (!dstRoot) return
  busy.value = true
  try {
    for (const p of Array.from(selected.value)) {
      const name = p.split('/').filter(Boolean).pop() || ''
      const dst = joinPath(dstRoot, name)
      await move(provider.value, p, dst, sourceId.value)
    }
    selected.value.clear()
    message.value = 'Moved selected'
    refresh()
  } catch (e: any) {
    error.value = e?.message || 'Bulk move failed'
  } finally {
    busy.value = false
  }
}

async function renameSelected() {
  if (selected.value.size !== 1) return
  const [p] = Array.from(selected.value)
  const parts = p.split('/')
  const oldName = parts.pop() || ''
  const base = parts.join('/') || '/'
  const newName = prompt('Rename to:', oldName)
  if (!newName || newName === oldName) return
  busy.value = true
  try {
    const dst = (base.endsWith('/') ? base : base + '/') + newName
    await move(provider.value, p, dst, sourceId.value)
    selected.value.clear()
    message.value = 'Renamed'
    refresh()
  } catch (e: any) {
    error.value = e?.message || 'Rename failed'
  } finally {
    busy.value = false
  }
}

async function softDeleteItem(it: FileItem) {
  await softDeleteMany([fullPath(it)])
}

async function softDeleteMany(paths: string[]) {
  if (paths.length === 0) return
  busy.value = true
  try {
    const trashDir = joinPath(currentPath.value, '.trash')
    try { await mkdir(provider.value, trashDir, sourceId.value) } catch {}
    const moves: { src: string; dst: string }[] = []
    for (const p of paths) {
      const name = p.split('/').filter(Boolean).pop() || ''
      const dst = joinPath(trashDir, name)
      await move(provider.value, p, dst, sourceId.value)
      moves.push({ src: p, dst })
    }
    selected.value.clear()
    message.value = 'Moved to trash'
    refresh()
    toasts.push({
      kind: 'success',
      text: `Moved to trash (${moves.length})`,
      actionLabel: 'Undo',
      action: async () => {
        for (const m of moves) await move(provider.value, m.dst, m.src, sourceId.value)
        refresh()
      },
      timeoutMs: 7000,
    })
  } catch (e: any) {
    error.value = e?.message || 'Trash failed'
    toasts.push({ kind: 'error', text: 'Trash failed' })
  } finally {
    busy.value = false
  }
}

async function showPreview(it: FileItem) {
  // Preview functionality removed with info panel
  toasts.push({ kind: 'info', text: 'Preview functionality removed' })
}

function selectAllVisible() {
  items.value.forEach(it => selected.value.add(it.path))
}

function clearSelection() {
  selected.value.clear()
}

function rowDblClick(it: FileItem) {
  if (it.kind === 'dir') {
    currentPath.value = it.path
    refresh()
  }
}

function toggleFoldersFirst() {
  foldersFirst.value = !foldersFirst.value
  localStorage.setItem('fb_foldersFirst', foldersFirst.value ? 'true' : 'false')
  refresh()
}

// Update document title when titleText changes
watch(titleText, (newTitle) => {
  document.title = newTitle
}, { immediate: true })

// Watch for route parameter changes
watch(() => route.query, (newQuery) => {
  console.log('Route query changed:', newQuery)
  const newProvider = (newQuery.provider as any) || 'fs'
  const newSourceId = (newQuery.sourceId as any) || ''
  
  if (newProvider !== provider.value || newSourceId !== sourceId.value) {
    console.log('Updating provider/sourceId:', { from: { provider: provider.value, sourceId: sourceId.value }, to: { provider: newProvider, sourceId: newSourceId } })
    provider.value = newProvider
    sourceId.value = newSourceId
    currentPath.value = '/'
    refresh()
  }
}, { immediate: false })

onMounted(async () => { await fetchCurrentUser(); await refresh() })
</script>

<style scoped>
.toolbar {
  box-shadow: 0 1px 2px rgba(0,0,0,0.06), 0 4px 8px rgba(0,0,0,0.04);
}


.icon-btn {
  transition: background-color .15s ease, border-color .15s ease, box-shadow .15s ease, transform .05s ease;
  box-shadow: 0 1px 0 rgba(255,255,255,0.6) inset, 0 1px 2px rgba(0,0,0,0.06);
}
.icon-btn:hover { background: #ffffff; border-color: #9ca3af; box-shadow: 0 1px 0 rgba(255,255,255,0.9) inset, 0 2px 4px rgba(0,0,0,0.08); }
.icon-btn:active { transform: translateY(1px); }
.icon-btn:focus { outline: none; box-shadow: 0 0 0 3px rgba(59,130,246,0.35); }

.search-box { transition: box-shadow .15s ease, border-color .15s ease, background-color .15s ease; }
.search-box:focus-within { border-color: #93c5fd; box-shadow: 0 0 0 3px rgba(59,130,246,0.25); background: #ffffff; }

.info-panel { box-shadow: 0 1px 2px rgba(0,0,0,0.06), 0 8px 16px rgba(0,0,0,0.06); }

.row-icon-btn {
  transition: background-color .15s ease, border-color .15s ease, box-shadow .15s ease;
}
.row-icon-btn:hover { background: #f9fafb !important; border-color: #cbd5e1 !important; }
.row-icon-btn:focus { outline: none; box-shadow: 0 0 0 2px rgba(59,130,246,0.30); }
.file-link { text-decoration: none; color: #1e3a8a; }
.file-link:hover { text-decoration: none; }
.file-table th, .file-table td { font-size: 14px; line-height: 1.4; }
.crumbs-inline :deep(a) { color: #1e3a8a; text-decoration: none; }
.crumbs-inline :deep(a:hover) { text-decoration: underline; }

.table-row { transition: background-color .12s ease; }
.table-row:hover td { background: #e2e8ff; }
.table-row.selected td { background: #dbeafe; }
.table-row.selected:hover td { background: #bfdbfe; }
.table-row:focus-within td { background: #eaefff; }
.table-row td { transition: background-color .12s ease; }
</style>
