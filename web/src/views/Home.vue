<template>
  <section>
    <h1 style="margin: 0 0 0.5rem 0;">Connectors</h1>
    <div style="display:flex; gap:12px; flex-wrap:wrap;">
        <div v-for="s in sources" :key="s.id" @click="open(s)" style="cursor:pointer; width:240px; border:1px solid #e5e7eb; border-radius:12px; padding:12px; background:#fff; display:flex; gap:12px; align-items:center; box-shadow:0 4px 10px rgba(0,0,0,0.05);">
          <div style="font-size:0; display:flex; align-items:center; justify-content:center; width:26px; height:26px;">
            <Icon :name="s.kind==='s3' ? 'cloud' : 'computer'" :size="22" color="#1e3a8a" />
          </div>
          <div style="display:flex; flex-direction:column; min-width:0;">
            <div style="font-weight:600; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">{{ s.name }}</div>
            <div style="color:#6b7280; font-size:12px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">{{ descriptor(s) }}</div>
          </div>
        </div>
        <div v-if="isAdmin" @click="showAdd=true" style="cursor:pointer; width:240px; border:1px dashed #cbd5e1; border-radius:12px; padding:12px; background:#f8fafc; display:flex; gap:12px; align-items:center; justify-content:center; color:#1e3a8a;">
          <div style="font-size:22px;">＋</div>
          <div style="font-weight:600;">Add Source</div>
        </div>
    </div>
    
    <!-- Add Source Modal -->
    <AddSourceModal 
      v-model="showAdd" 
      :show-browse-button="true" 
      @submit="handleCreateSource" 
      @browse="showFolderPicker = true" 
    />
    
    <!-- Folder Picker Modal -->
    <FolderPickerModal 
      v-model="showFolderPicker"
      :initial-path="newFs.root || '/'"
      @select="onFolderSelected"
    />
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { clearToken, authHeaders } from '@/lib/auth'
import type { User } from '@/lib/auth'
import Icon from '@/components/Icon.vue'
import { useRouter } from 'vue-router'
import { addRecentSource } from '@/lib/prefs'
import { createSource, type SourceKind } from '@/lib/sources'
import { usePermissions } from '@/composables/usePermissions'
import FolderPickerModal from '@/components/FolderPickerModal.vue'
import AddSourceModal from '@/components/AddSourceModal.vue'
const router = useRouter()
const { isAdmin, fetchCurrentUser } = usePermissions()

const sources = ref<{id:number, kind:string, name:string}[]>([])
const me = ref<User | null>(null)
const adminSourcesMap = ref<Record<number, any>>({})
const showAdd = ref(false)
const busy = ref(false)

const newFs = ref<{root:string}>({ root: '' })
const showFolderPicker = ref(false)

async function load() {
  const { authenticatedApiRequest } = await import('@/lib/api')
  const res = await authenticatedApiRequest('/v1/sources')
  if (res.ok) sources.value = await res.json()
  const r2 = await authenticatedApiRequest('/v1/auth/me')
  if (r2.ok) {
    me.value = (await r2.json()).user
    await fetchCurrentUser()
  }
  if (isAdmin.value) {
    const ra = await authenticatedApiRequest('/v1/admin/sources')
    if (ra.ok) {
      const rows = await ra.json()
      const map: Record<number, any> = {}
      for (const r of rows) { try { map[r.id] = JSON.parse(r.config||'{}') } catch { map[r.id] = {} } }
      adminSourcesMap.value = map
    }
  }
}

function open(s: any) {
  // record recent source for quick access in sidebar
  try { addRecentSource({ id: s.id, name: s.name, kind: s.kind }) } catch {}
  router.push({ path: '/browser', query: { provider: s.kind, sourceId: s.id } })
}

function logout() { clearToken(); window.location.href = '/login' }

onMounted(load)

function descriptor(s:any){
  if (s.kind==='s3') return 'Amazon S3 Bucket'
  const cfg = adminSourcesMap.value[s.id]
  return cfg?.root ? `Local Filesystem — ${cfg.root}` : 'Local Filesystem'
}



async function handleCreateSource(data: { kind: SourceKind, name: string, config: any }) {
  if (busy.value) return
  busy.value = true
  try {
    await createSource(data.kind, data.name, data.config)
    showAdd.value = false
    await load()
  } catch (e:any) {
    console.error('Failed to create source:', e?.message || 'Unknown error')
  } finally {
    busy.value = false
  }
}

function onFolderSelected(path: string) {
  newFs.value.root = path
  showFolderPicker.value = false
}


</script>
