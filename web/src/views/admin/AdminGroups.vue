<template>
  <section>
    <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:16px;">
      <h2 style="margin:0;">Groups</h2>
      <button @click="showAddModal = true" style="display:flex; align-items:center; gap:6px; padding:8px 12px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff; cursor:pointer;">
        <Icon name="plus" :size="16" color="#fff" />
        Add Group
      </button>
    </div>
    <table class="admin-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Members</th>
            <th>Add Member</th>
            <th>Actions</th>
          </tr>
        </thead>
      <tbody>
        <tr v-for="g in groups" :key="g.id">
          <td>{{ g.name }}</td>
          <td>
            <div style="display:flex; gap:4px; flex-wrap:wrap;">
              <span v-for="m in members[g.id] || []" :key="m" style="background:#eef2ff; color:#1e3a8a; padding:2px 6px; border-radius:12px; display:inline-flex; align-items:center; gap:6px;">
                {{ getUserEmail(m) }}
                <button @click="removeMember(g, m)" style="border:0; background:transparent; cursor:pointer;">×</button>
              </span>
            </div>
          </td>
          <td>
            <div style="display:flex; gap:8px; align-items:center;">
              <select v-model="selectedUser[g.id]" style="padding:6px; border:1px solid #e5e7eb; border-radius:6px; flex:1;">
                <option value="">Select User to add</option>
                <option v-for="u in users" :key="u.sub" :value="u.sub" :disabled="(members[g.id] || []).includes(u.sub)">{{ u.email }} ({{ u.name||'-' }})</option>
              </select>
              <button @click="addMember(g)" style="padding:6px; border:1px solid #10b981; border-radius:4px; background:#ecfdf5; cursor:pointer; display:flex; align-items:center;" title="Add Member">
                <Icon name="plus" :size="16" color="#059669" />
              </button>
            </div>
          </td>
          <td>
            <div style="display:flex; gap:8px; justify-content:center; align-items:center;">
              <button @click="editGroup(g)" style="padding:6px; border:1px solid #3b82f6; border-radius:4px; background:#dbeafe; cursor:pointer; display:flex; align-items:center;" title="Edit Group">
                <Icon name="edit" :size="16" color="#2563eb" />
              </button>
              <button @click="deleteGroup(g)" style="padding:6px; border:1px solid #fca5a5; border-radius:4px; background:#fee2e2; cursor:pointer; display:flex; align-items:center;" title="Delete Group">
                <Icon name="trash" :size="16" color="#dc2626" />
              </button>
            </div>
          </td>
        </tr>
        <tr v-if="groups.length === 0">
          <td colspan="4" style="text-align:center; padding:40px; color:#6b7280; font-style:italic;">
            No groups created yet. Click "Add Group" to create your first group.
          </td>
        </tr>
      </tbody>
    </table>
    <!-- Group Modal -->
    <GroupModal
      v-model="showAddModal"
      mode="add"
      :users="users"
      @submit="handleAddGroup"
    />
    
    <GroupModal
      v-model="showEditModal"
      mode="edit"
      :group="selectedGroup"
      :users="users"
      @submit="handleEditGroup"
    />

    <!-- Delete Group Modal -->
    <Modal v-model="showDeleteModal" title="Delete Group">
      <div style="display:flex; flex-direction:column; gap:16px;">
        <p style="margin:0; color:#374151;">Are you sure you want to delete the group <strong>{{ deletingGroup?.name }}</strong>? This action cannot be undone.</p>
      </div>
      <template #footer>
        <div style="display:flex; gap:8px; justify-content:flex-end;">
          <button @click="showDeleteModal = false" style="padding:8px 16px; border:1px solid #e5e7eb; border-radius:6px; background:#fff; cursor:pointer;">Cancel</button>
          <button @click="confirmDelete" style="padding:8px 16px; border:1px solid #dc2626; border-radius:6px; background:#dc2626; color:#fff; cursor:pointer;">Delete Group</button>
        </div>
      </template>
    </Modal>
    <ToastContainer />
  </section>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'
import { useToasts } from '@/lib/toasts'
import ToastContainer from '@/components/ToastContainer.vue'
import Modal from '@/components/Modal.vue'
import Icon from '@/components/Icon.vue'
import GroupModal from '@/components/GroupModal.vue'
import type { GroupFormData } from '@/types/admin-modals'

const groups = ref<any[]>([])
const users = ref<any[]>([])
const members = ref<Record<number,string[]>>({})
const selectedUser = ref<Record<number,string>>({})
const showAddModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const selectedGroup = ref<any>(null)
const deletingGroup = ref<any>(null)
const toasts = useToasts()

async function load(){
  const res = await authenticatedApiRequest('/v1/admin/groups')
  if (res.ok) groups.value = await res.json()
  const ur = await authenticatedApiRequest('/v1/admin/users')
  if (ur.ok) users.value = await ur.json()
  // fetch members per group
  for (const g of groups.value) {
    const mr = await authenticatedApiRequest(`/v1/admin/groups/${g.id}/members`)
    if (mr.ok) members.value[g.id] = await mr.json()
    // Initialize selectedUser for each group
    selectedUser.value[g.id] = ''
  }
}
function editGroup(group: any) {
  selectedGroup.value = group
  showEditModal.value = true
}

function handleAddGroup(groupData: GroupFormData) {
  authenticatedApiRequest('/v1/admin/groups', { 
    method: 'POST', 
    headers: { 'Content-Type': 'application/json' }, 
    body: JSON.stringify({ name: groupData.name }) 
  })
  .then(res => {
    if (res.ok) {
      load()
      toasts.push({ kind: 'success', text: 'Group created' })
    } else {
      toasts.push({ kind: 'error', text: 'Create failed' })
    }
  })
}

function handleEditGroup(groupData: GroupFormData) {
  if (selectedGroup.value) {
    authenticatedApiRequest(`/v1/admin/groups/${selectedGroup.value.id}`, { 
      method: 'PUT', 
      headers: { 'Content-Type': 'application/json' }, 
      body: JSON.stringify({ name: groupData.name }) 
    })
    .then(res => {
      if (res.ok) {
        selectedGroup.value = null
        load()
        toasts.push({ kind: 'success', text: 'Group updated' })
      } else {
        toasts.push({ kind: 'error', text: 'Update failed' })
      }
    })
  }
}

function deleteGroup(group: any) {
  deletingGroup.value = group
  showDeleteModal.value = true
}

async function confirmDelete() {
  const res = await authenticatedApiRequest(`/v1/admin/groups/${deletingGroup.value.id}`, { 
    method: 'DELETE'
  })
  if (res.ok) { 
    showDeleteModal.value = false; 
    deletingGroup.value = null; 
    load(); 
    toasts.push({ kind: 'success', text: 'Group deleted' }) 
  } else {
    toasts.push({ kind: 'error', text: 'Delete failed' })
  }
}
function getUserEmail(sub: string) {
  const user = users.value.find(u => u.sub === sub)
  return user ? user.email : sub
}

async function addMember(g:any){
  const sub = selectedUser.value[g.id]
  if (!sub) return
  const res = await authenticatedApiRequest(`/v1/admin/groups/${g.id}/members`, { method:'POST', headers: { 'Content-Type':'application/json' }, body: JSON.stringify({ sub }) })
  if (res.ok){ await load(); toasts.push({ kind:'success', text:'Member added' }) } else toasts.push({ kind:'error', text:'Add failed' })
}
async function removeMember(g:any, sub:string){
  const res = await authenticatedApiRequest(`/v1/admin/groups/${g.id}/members/${encodeURIComponent(sub)}`, { method:'DELETE' })
  if (res.ok){ await load(); toasts.push({ kind:'success', text:'Member removed' }) } else toasts.push({ kind:'error', text:'Remove failed' })
}
onMounted(load)
</script>
