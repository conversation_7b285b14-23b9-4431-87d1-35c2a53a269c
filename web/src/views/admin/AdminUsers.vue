<template>
  <div class="admin-users">
    <div style="display:flex; align-items:center; justify-content:space-between; margin:0 0 10px 0;">
      <h2 style="margin:0;">User Management</h2>
      <button @click="showAddModal = true" style="padding:8px 12px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff;">Add User</button>
    </div>

    <div class="table-container">
      <table class="admin-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Status</th>
            <th>Roles</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="u in users" :key="u.sub">
            <td>{{ u.name || '-' }}</td>
            <td>{{ u.email }}</td>
            <td style="text-align:center;">
              <span v-if="u.blocked" style="color:#ef4444; font-weight:500;">Blocked</span>
              <span v-else-if="u.active" style="color:#10b981; font-weight:500;">Active</span>
              <span v-else style="color:#f59e0b; font-weight:500;">Inactive</span>
            </td>
            <td>
              <div style="display:flex; gap:4px; flex-wrap:wrap;">
                <span v-for="role in u.roles || []" :key="role.id" 
                      style="padding:2px 6px; border-radius:12px; font-size:11px; font-weight:500; background:#f3e8ff; color:#7c3aed; border:1px solid #a855f7;"
                      :title="role.description">
                  {{ role.name }}
                </span>
                <span v-if="!u.roles || u.roles.length === 0" style="color:#6b7280; font-style:italic;">No roles</span>
              </div>
            </td>
            <td class="actions">
              <div style="display:flex; gap:8px; justify-content:center;">
                <button @click="editUser(u)" style="padding:6px; border:1px solid #3b82f6; border-radius:4px; background:#dbeafe; cursor:pointer; display:flex; align-items:center;" title="Edit User">
                  <Icon name="edit" :size="16" color="#2563eb" />
                </button>
                <button @click="manageUserRoles(u)" style="padding:6px; border:1px solid #a855f7; border-radius:4px; background:#f3e8ff; cursor:pointer; display:flex; align-items:center;" title="Manage Roles">
                  <Icon name="users" :size="16" color="#7c3aed" />
                </button>
                <button @click="viewUserSources(u)" 
                        style="padding:6px; border:1px solid #6b7280; border-radius:4px; background:#f9fafb; cursor:pointer; display:flex; align-items:center;" 
                        :title="`View source access for ${u.name || u.email}`">
                  <Icon name="folder" :size="16" color="#6b7280" />
                </button>
                <!-- Status Toggle Button -->
                <button v-if="!hasAdminRole(u)" @click="toggleUserStatus(u)" 
                        :style="getStatusButtonStyle(u)" 
                        :title="getStatusButtonTitle(u)">
                  <Icon :name="getStatusButtonIcon(u)" :size="16" :color="getStatusButtonIconColor(u)" />
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="users.length === 0">
            <td colspan="5" style="text-align:center; padding:40px; color:#6b7280; font-style:italic;">
              No users found. Users will appear here once they sign in.
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- User Modal -->
    <UserModal
      v-model="showAddModal"
      mode="add"
      @submit="handleAddUser"
    />
    
    <UserModal
      v-model="showEditModal"
      mode="edit"
      :user="selectedUser"
      @submit="handleEditUser"
    />

    <!-- User Role Modal -->
    <UserRoleModal
      v-model="showUserRoleModal"
      :user="selectedUserForRoles"
      @submit="handleUserRolesUpdate"
    />

    <!-- User Sources Modal -->
    <UserSourcesModal
      v-model="showSourcesModal"
      :user="selectedUser"
    />

    <ToastContainer />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'
import { useToasts } from '@/lib/toasts'
import ToastContainer from '@/components/ToastContainer.vue'
import Icon from '@/components/Icon.vue'
import UserModal from '@/components/UserModal.vue'
import UserRoleModal from '../../components/UserRoleModal.vue'
import UserSourcesModal from '../../components/UserSourcesModal.vue'
import type { UserFormData } from '@/types/admin-modals'

const users = ref<any[]>([])
const showAddModal = ref(false)
const showEditModal = ref(false)
const selectedUser = ref<any>(null)
const showUserRoleModal = ref(false)
const selectedUserForRoles = ref<any>(null)
const showSourcesModal = ref(false)
const toasts = useToasts()

async function load(){
  console.log('🚀 Starting load function for AdminUsers')
  const res = await authenticatedApiRequest('/v1/admin/users')
  console.log('📋 Admin users API response:', res.status, res.statusText)
  if (res.ok) {
    const userList = await res.json()
    console.log('👥 User list received:', userList.length, 'users')
    // Load source access and roles for each user
    for (const user of userList) {
      try {
        console.log(`🔍 Loading sources for user ${user.sub} (${user.email})`)
        const sourcesRes = await authenticatedApiRequest(`/v1/admin/users/${user.sub}/sources`)
        console.log(`📡 Sources API response for ${user.sub}:`, sourcesRes.status, sourcesRes.statusText)
        if (sourcesRes.ok) {
          const sourcesData = await sourcesRes.json()
          console.log(`✅ Sources data for ${user.sub}:`, sourcesData)
          user.sources = sourcesData
        } else {
          console.error(`❌ Failed to load sources for user ${user.sub}:`, sourcesRes.status, sourcesRes.statusText)
          user.sources = []
          // Show error toast for authentication issues
          if (sourcesRes.status === 401) {
            toasts.push({ kind: 'error', text: 'Authentication failed when loading user sources' })
          } else if (sourcesRes.status === 403) {
            toasts.push({ kind: 'error', text: 'Access denied when loading user sources' })
          }
        }
      } catch (e) {
        console.error(`💥 Error loading sources for user ${user.sub}:`, e)
        user.sources = []
      }
      
      try {
        const rolesRes = await authenticatedApiRequest(`/v1/admin/users/${user.sub}/roles`)
        if (rolesRes.ok) {
          const roleData = await rolesRes.json()
          // Fetch role details for each role ID
          const roleDetails = []
          for (const roleId of roleData.roles || []) {
            try {
              const roleDetailRes = await authenticatedApiRequest(`/v1/admin/roles/${roleId}`)
              if (roleDetailRes.ok) {
                const roleDetail = await roleDetailRes.json()
                roleDetails.push(roleDetail)
              }
            } catch (e) {
              // Skip failed role lookups
            }
          }
          user.roles = roleDetails
        } else {
          user.roles = []
        }
      } catch (e) {
        user.roles = []
      }
    }
    users.value = userList
  }
}

function getSourceChipStyle(kind: string) {
  const baseStyle = 'padding:2px 6px; border-radius:12px; font-size:11px; font-weight:500;'
  switch (kind) {
    case 'fs':
      return baseStyle + ' background:#ecfdf5; color:#065f46; border:1px solid #10b981;'
    case 's3':
      return baseStyle + ' background:#eff6ff; color:#1e40af; border:1px solid #3b82f6;'
    default:
      return baseStyle + ' background:#f3f4f6; color:#374151; border:1px solid #9ca3af;'
  }
}
async function block(u:any){
  await authenticatedApiRequest(`/v1/admin/users/${u.sub}/block`, { method:'POST' })
  toasts.push({ kind:'success', text:'User blocked' })
  load()
}
async function unblock(u:any){
  await authenticatedApiRequest(`/v1/admin/users/${u.sub}/unblock`, { method:'POST' })
  toasts.push({ kind:'success', text:'User unblocked' })
  load()
}
async function activate(u:any){
  await authenticatedApiRequest(`/v1/admin/users/${u.sub}/activate`, { method:'POST' })
  toasts.push({ kind:'success', text:'User activated' })
  load()
}
async function deactivate(u:any){
  await authenticatedApiRequest(`/v1/admin/users/${u.sub}/deactivate`, { method:'POST' })
  toasts.push({ kind:'success', text:'User deactivated' })
  load()
}

// Unified status toggle function
async function toggleUserStatus(user: any) {
  if (user.blocked) {
    await unblock(user)
  } else if (user.active) {
    await deactivate(user)
  } else {
    await activate(user)
  }
}

// Helper functions for unified button
function getStatusButtonStyle(user: any) {
  const baseStyle = 'padding:6px; border-radius:4px; cursor:pointer; display:flex; align-items:center;'
  if (user.blocked) {
    return baseStyle + ' border:1px solid #dc2626; background:#fee2e2;'
  } else if (user.active) {
    return baseStyle + ' border:1px solid #f59e0b; background:#fef3c7;'
  } else {
    return baseStyle + ' border:1px solid #10b981; background:#ecfdf5;'
  }
}

function getStatusButtonTitle(user: any) {
  if (user.blocked) return 'Unblock User'
  if (user.active) return 'Deactivate User'
  return 'Activate User'
}

function getStatusButtonIcon(user: any) {
  if (user.blocked) return 'check'
  if (user.active) return 'pause'
  return 'check'
}

function getStatusButtonIconColor(user: any) {
  if (user.blocked) return '#059669'
  if (user.active) return '#d97706'
  return '#059669'
}
async function makeAdmin(u:any){
  await authenticatedApiRequest(`/v1/admin/users/${u.sub}/admin`, { method:'POST' })
  toasts.push({ kind:'success', text:'User promoted to admin' })
  load()
}
function editUser(user: any) {
  selectedUser.value = user
  showEditModal.value = true
}

function handleAddUser(userData: UserFormData) {
  const newUser = {
    sub: Date.now().toString(),
    name: userData.name,
    email: userData.email,
    roles: userData.roles || [],
    blocked: false,
    sources: []
  }
  users.value.push(newUser)
  toasts.push({ kind:'success', text:'User added successfully' })
}

function handleEditUser(userData: UserFormData) {
  if (selectedUser.value) {
    const index = users.value.findIndex(u => u.sub === selectedUser.value!.sub)
    if (index !== -1) {
      users.value[index] = {
        ...users.value[index],
        name: userData.name,
        email: userData.email,
        roles: userData.roles || []
      }
      toasts.push({ kind:'success', text:'User updated successfully' })
    }
  }
}

async function removeAdmin(u:any){
  // Check if there's at least one other active admin
  const activeAdmins = users.value.filter(user => hasAdminRole(user) && !user.blocked && user.sub !== u.sub)
  
  if (activeAdmins.length === 0) {
    toasts.push({ kind:'error', text:'Cannot remove admin privileges. At least one active admin must remain.' })
    return
  }
  
  await authenticatedApiRequest(`/v1/admin/users/${u.sub}/admin`, { method:'DELETE' })
  toasts.push({ kind:'success', text:'Admin privileges removed' })
  load()
}

function manageUserRoles(user: any) {
  selectedUserForRoles.value = user
  showUserRoleModal.value = true
}

async function handleUserRolesUpdate(roleData: any) {
  if (selectedUserForRoles.value) {
    try {
      // Extract role IDs from role objects
      const roleIds = roleData.roles.map((role: any) => role.id || role)
      
      const res = await authenticatedApiRequest(`/v1/admin/users/${selectedUserForRoles.value.sub}/roles`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roles: roleIds })
      })
      
      if (res.ok) {
        // Update local state with the new roles
        const index = users.value.findIndex(u => u.sub === selectedUserForRoles.value!.sub)
        if (index !== -1) {
          users.value[index].roles = roleData.roles
        }
        toasts.push({ kind:'success', text:'User roles updated successfully' })
      } else {
        const errorData = await res.json().catch(() => ({}))
        toasts.push({ kind:'error', text: errorData.error?.message || 'Failed to update user roles' })
      }
    } catch (error) {
      toasts.push({ kind:'error', text:'Error updating user roles' })
    }
  }
}

function hasAdminRole(user: any): boolean {
  return user.roles?.some((role: any) => role.id === 'admin' || role.name === 'admin') || false
}

function viewUserSources(user: any) {
  selectedUser.value = user
  showSourcesModal.value = true
}

onMounted(load)
</script>
