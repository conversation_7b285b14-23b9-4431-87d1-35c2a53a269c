<template>
  <section>
    <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:16px;">
      <h2 style="margin:0;">Sources</h2>
      <button @click="showAddModal = true" style="display:flex; align-items:center; gap:6px; padding:8px 12px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff; cursor:pointer;">
        <Icon name="plus" :size="16" color="#fff" />
        Add Source
      </button>
    </div>
    <table class="admin-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Kind</th>
          <th>Root Folder Path</th>
          <th style="text-align:center;">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="s in sources" :key="s.id">
          <td>{{ s.name }}</td>
          <td style="text-transform:uppercase;">{{ s.kind }}</td>
          <td style="font-family:monospace; font-size:13px; max-width:300px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap;" :title="getRootPath(s)">
            {{ getRootPath(s) }}
          </td>
          <td style="text-align:center;">
            <div style="display:flex; gap:8px; justify-content:center;">
              <button @click="edit(s)" style="padding:6px; border:1px solid #3b82f6; border-radius:4px; background:#dbeafe; cursor:pointer; display:flex; align-items:center;" title="Edit">
                <Icon name="edit" :size="16" color="#2563eb" />
              </button>
              <button @click="remove(s)" style="padding:6px; border:1px solid #fca5a5; border-radius:4px; background:#fee2e2; cursor:pointer; display:flex; align-items:center;" title="Delete">
                <Icon name="trash" :size="16" color="#dc2626" />
              </button>
              <button @click="test(s)" style="padding:6px; border:1px solid #10b981; border-radius:4px; background:#ecfdf5; cursor:pointer; display:flex; align-items:center;" title="Test Access">
                <Icon name="test" :size="16" color="#059669" />
              </button>
            </div>
          </td>
        </tr>
        <tr v-if="sources.length === 0">
          <td colspan="4" style="text-align:center; padding:40px; color:#6b7280; font-style:italic;">
            No sources configured yet. Click "Add Source" to get started.
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Source Modal -->
    <SourceModal
      v-model="showAddModal"
      mode="add"
      @submit="handleAddSource"
    />
    
    <SourceModal
      v-model="showEditModal"
      mode="edit"
      :source="selectedSource"
      @submit="handleEditSource"
    />

    <!-- Delete Confirmation Modal -->
    <Modal v-model="showDeleteModal" title="Confirm Delete">
      <div v-if="deletingSource">
        <p>Are you sure you want to delete the source "{{ deletingSource.name }}"?</p>
        <p style="color:#dc2626; font-size:14px;">This action cannot be undone.</p>
      </div>
      <template #footer>
        <div style="display:flex; gap:8px; justify-content:flex-end;">
          <button @click="showDeleteModal = false" style="padding:8px 16px; border:1px solid #e5e7eb; border-radius:6px; background:#fff; cursor:pointer;">Cancel</button>
          <button @click="deleteSource" style="padding:8px 16px; border:1px solid #dc2626; border-radius:6px; background:#dc2626; color:#fff; cursor:pointer;">Delete</button>
        </div>
      </template>
    </Modal>
  </section>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'
import { useToasts } from '@/lib/toasts'
import Modal from '@/components/Modal.vue'
import Icon from '@/components/Icon.vue'
import SourceModal from '@/components/SourceModal.vue'
import { removeRecentSource } from '@/lib/prefs'
import type { SourceKind } from '@/lib/sources'
import type { SourceFormData } from '@/types/admin-modals'

const { push: pushToast } = useToasts()

const sources = ref<{id:number, name:string, kind:string, config?:string}[]>([])
async function reload(){
  const res = await authenticatedApiRequest('/v1/admin/sources')
  if (res.ok) sources.value = await res.json()
}
onMounted(reload)

const showAddModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const selectedSource = ref<any>(null)
const deletingSource = ref<any>(null)

const testResults = ref<Record<number,{ok:boolean,message:string}>>({})

async function handleAddSource(data: SourceFormData) {
  try {
    const { createSource } = await import('@/lib/sources')
    await createSource(data.kind, data.name, data.config)
    await reload()
    pushToast({ kind:'success', text:'Source created' })
  } catch (e:any) {
    pushToast({ kind:'error', text: e?.message || 'Create failed' })
  }
}

async function handleEditSource(data: SourceFormData) {
  if (!selectedSource.value) return
  try {
    const res = await authenticatedApiRequest(`/v1/sources/${selectedSource.value.id}`, { 
      method:'PUT', 
      headers: { 'Content-Type':'application/json' }, 
      body: JSON.stringify({ 
        name: data.name, 
        config: data.config 
      }) 
    })
    if (res.ok) { 
      await reload()
      pushToast({ kind:'success', text:'Source updated' }) 
    } else {
      pushToast({ kind:'error', text:'Update failed' })
    }
  } catch (e:any) {
    pushToast({ kind:'error', text: e?.message || 'Update failed' })
  }
}



function edit(s:any) {
  selectedSource.value = s
  showEditModal.value = true
}



function remove(s:any){
  deletingSource.value = s
  showDeleteModal.value = true
}

async function deleteSource(){
  if (!deletingSource.value) return
  const res = await authenticatedApiRequest(`/v1/sources/${deletingSource.value.id}`, { method:'DELETE' })
  if (res.ok) { 
    removeRecentSource(deletingSource.value.id)
    showDeleteModal.value = false
    deletingSource.value = null
    await reload()
    pushToast({ kind:'success', text:'Source deleted' }) 
  } else {
    pushToast({ kind:'error', text:'Delete failed' })
  }
}

async function test(s:any) {
  const res = await authenticatedApiRequest(`/v1/sources/${s.id}/test`, { 
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({})
  })
  if (res.ok) {
    const data = await res.json()
    testResults.value[s.id] = { ok:true, message: data.message || 'OK' }
    pushToast({ kind:'success', text: `Test successful: ${data.message || 'Connection OK'}` })
  } else {
    const err = await res.json().catch(()=>({ error:{ message:'Failed'}}))
    testResults.value[s.id] = { ok:false, message: err?.error?.message || 'Test failed' }
    pushToast({ kind:'error', text: `Test failed: ${err?.error?.message || 'Connection failed'}` })
  }
}
function chipStyle(ok:boolean){
  return ok ? 'background:#ecfdf5; color:#065f46; border:1px solid #10b981; padding:2px 8px; border-radius:12px; font-size:12px;'
            : 'background:#fef2f2; color:#7f1d1d; border:1px solid #ef4444; padding:2px 8px; border-radius:12px; font-size:12px;'
}

function getRootPath(source: any): string {
  if (!source.config) return 'N/A'
  try {
    const config = typeof source.config === 'string' ? JSON.parse(source.config) : source.config
    if (source.kind === 'fs') {
      return config.root || 'N/A'
    } else if (source.kind === 's3') {
      const bucket = config.bucket || ''
      const prefix = config.rootPrefix || ''
      return prefix ? `s3://${bucket}/${prefix}` : `s3://${bucket}/`
    }
  } catch (e) {
    return 'Invalid config'
  }
  return 'N/A'
}
</script>
