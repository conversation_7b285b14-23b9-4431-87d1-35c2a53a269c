<template>
  <div>
    <!-- Header -->
    <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:24px;">
      <h1 style="font-size:24px; font-weight:700; color:#111827; margin:0;">Permissions</h1>
      <button 
        @click="handleAddPermission"
        style="padding:8px 16px; border:none; border-radius:6px; background:#3b82f6; color:#fff; font-weight:500; cursor:pointer;"
      >
        Add Permission
      </button>
    </div>

    <!-- Actions Table -->
    <table class="admin-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Description</th>
          <th>Resource Type</th>
          <th>Created</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="permission in permissions" :key="permission.id">
          <td style="font-weight:500;">{{ permission.name }}</td>
          <td>{{ permission.description || '-' }}</td>
          <td>
            <span style="padding:2px 8px; border-radius:12px; font-size:12px; font-weight:500; background:#f3f4f6; color:#374151; border:1px solid #9ca3af;">
              {{ permission.resource_type }}
            </span>
          </td>
          <td style="color:#6b7280; font-size:14px;">{{ formatDate(permission.created_at) }}</td>
          <td>
            <div style="display:flex; gap:8px; justify-content:center;">
              <button 
                @click="editPermission(permission)"
                style="padding:6px; border:1px solid #3b82f6; border-radius:4px; background:#dbeafe; cursor:pointer; display:flex; align-items:center;" 
                title="Edit Permission"
              >
                <Icon name="edit" :size="16" color="#2563eb" />
              </button>
              <button 
                @click="deletePermission(permission.id)"
                style="padding:6px; border:1px solid #fca5a5; border-radius:4px; background:#fee2e2; cursor:pointer; display:flex; align-items:center;" 
                title="Delete Permission"
              >
                <Icon name="trash" :size="16" color="#dc2626" />
              </button>
            </div>
          </td>
        </tr>
        <tr v-if="permissions.length === 0">
          <td colspan="5" style="text-align:center; color:#6b7280; font-style:italic; padding:32px;">
            No permissions configured yet. Click "Add Permission" to create your first permission.
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Permission Modal -->
    <PermissionModal
      v-model:show="showModal"
      :mode="modalMode"
      :permission="selectedAction"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import PermissionModal from '@/components/PermissionModal.vue'
import Icon from '@/components/Icon.vue'
import type { Permission, PermissionFormData } from '@/types/admin-modals'
import { useToasts } from '@/lib/toasts'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'

const toasts = useToasts()

// State
const permissions = ref<Permission[]>([])
const showModal = ref(false)
const modalMode = ref<'add' | 'edit'>('add')
const selectedAction = ref<Permission | undefined>()
const loading = ref(false)

// Mock data for development
const mockPermissions: Permission[] = [
  {
    id: '1',
    name: 'read',
    description: 'Read access to resources',
    resource_type: 'file',
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: '2', 
    name: 'write',
    description: 'Write access to resources',
    resource_type: 'file',
    created_at: '2024-01-15T10:31:00Z'
  },
  {
    id: '3',
    name: 'delete',
    description: 'Delete access to resources', 
    resource_type: 'file',
    created_at: '2024-01-15T10:32:00Z'
  },
  {
    id: '4',
    name: 'admin',
    description: 'Administrative access',
    resource_type: 'system',
    created_at: '2024-01-15T10:33:00Z'
  }
]

// Load actions
const load = async () => {
  try {
    loading.value = true
    const response = await authenticatedApiRequest('/v1/admin/permissions')
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    
    const data = await response.json()
    permissions.value = data.map((permission: any) => ({
      id: permission.id,
      name: permission.name,
      description: permission.description,
      resource_type: permission.category,
      created_at: new Date().toISOString() // API doesn't return created_at yet
    }))
  } catch (error) {
    console.error('Failed to load actions:', error)
    toasts.push({ text: 'Failed to load actions', kind: 'error' })
  } finally {
    loading.value = false
  }
}

// Format date
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString()
}

// Handle add permission
const handleAddPermission = () => {
  modalMode.value = 'add'
  selectedAction.value = undefined
  showModal.value = true
}

// Handle edit permission
const editPermission = (permission: Permission) => {
  modalMode.value = 'edit'
  selectedAction.value = permission
  showModal.value = true
}

// Handle delete permission
const deletePermission = async (permissionId: string) => {
  if (!confirm('Are you sure you want to delete this permission?')) {
    return
  }
  
  try {
    const response = await authenticatedApiRequest(`/v1/admin/permissions/${permissionId}`, {
      method: 'DELETE'
    })
    
    if (response.ok) {
      permissions.value = permissions.value.filter(p => p.id !== permissionId)
      toasts.push({ text: 'Permission deleted successfully', kind: 'success' })
    } else {
      toasts.push({ text: 'Failed to delete permission', kind: 'error' })
    }
  } catch (error) {
    console.error('Failed to delete permission:', error)
    toasts.push({ text: 'Failed to delete permission', kind: 'error' })
  }
}

// Handle modal submit
const handleSubmit = async (formData: PermissionFormData) => {
  try {
    if (modalMode.value === 'add') {
      const response = await authenticatedApiRequest('/v1/admin/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        const result = await response.json()
        const newPermission: Permission = {
          id: result.id,
          ...formData,
          created_at: new Date().toISOString()
        }
        permissions.value.push(newPermission)
        toasts.push({ text: 'Permission created successfully', kind: 'success' })
      } else {
        toasts.push({ text: 'Failed to create permission', kind: 'error' })
      }
    } else {
      const response = await authenticatedApiRequest(`/v1/admin/permissions/${selectedAction.value?.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        const index = permissions.value.findIndex(p => p.id === selectedAction.value?.id)
        if (index !== -1) {
          permissions.value[index] = {
            ...permissions.value[index],
            ...formData,
            updated_at: new Date().toISOString()
          }
        }
        toasts.push({ text: 'Permission updated successfully', kind: 'success' })
      } else {
        toasts.push({ text: 'Failed to update permission', kind: 'error' })
      }
    }
    
    showModal.value = false
  } catch (error) {
    console.error('Failed to save permission:', error)
    toasts.push({ text: 'Failed to save permission', kind: 'error' })
  }
}

// Handle modal cancel
const handleCancel = () => {
  showModal.value = false
}

// Load data on mount
onMounted(() => {
  load()
})
</script>