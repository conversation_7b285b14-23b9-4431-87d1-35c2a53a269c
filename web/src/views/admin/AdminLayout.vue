<template>
  <div style="display:flex; gap:12px;">
    <aside style="width:240px; border:1px solid #d1d5db; border-radius:12px; background:#f3f4f6; padding:12px; height:calc(100vh - 40px); position:sticky; top:12px; display:flex; flex-direction:column; box-shadow: 0 8px 24px rgba(0,0,0,0.08);">
      <div style="font-weight:700; font-size:14px; color:#111827; padding:8px 12px;">Administration</div>
      <nav style="display:flex; flex-direction:column; gap:6px; padding-left:10px;">
        <router-link to="/admin/sources" style="text-decoration:none; color:#111827; padding:8px 12px; border-radius:8px;">Sources</router-link>
        <router-link to="/admin/users" style="text-decoration:none; color:#111827; padding:8px 12px; border-radius:8px;">Users</router-link>
        <router-link to="/admin/groups" style="text-decoration:none; color:#111827; padding:8px 12px; border-radius:8px;">Groups</router-link>
        <router-link to="/admin/roles" style="text-decoration:none; color:#111827; padding:8px 12px; border-radius:8px;">Roles</router-link>
        <router-link to="/admin/permissions" style="text-decoration:none; color:#111827; padding:8px 12px; border-radius:8px;">Permissions</router-link>
        <router-link to="/admin/policies" style="text-decoration:none; color:#111827; padding:8px 12px; border-radius:8px;">Policies</router-link>
      </nav>
    </aside>
    <section style="flex:1; min-width:0;">
      <router-view />
    </section>
  </div>
</template>

