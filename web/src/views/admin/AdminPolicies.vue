<template>
  <section>
    <div style="display:flex; align-items:center; justify-content:space-between; margin:0 0 10px 0;">
      <h2 style="margin:0;">Policies</h2>
      <button @click="showAdd=true" style="padding:8px 12px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff;">Add Policy</button>
    </div>
    <table class="admin-table">
      <thead>
        <tr>
          <th>Subject</th>
          <th>Source</th>
          <th>Path</th>
          <th>Action</th>
          <th>Effect</th>
          <th>Expiry</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="p in policies" :key="p.id">
          <td>{{ p.subject_type }} {{ p.subject_id||'' }}</td>
          <td>{{ sourceName(p.source_id) }}</td>
          <td>{{ p.path_prefix }}</td>
          <td>{{ p.action }}</td>
          <td>{{ p.effect }}</td>
          <td>{{ p.expires_at || '-' }}</td>
          <td>
            <div style="display:flex; gap:8px;">
              <button @click="editPolicy(p)" style="display:flex; align-items:center; gap:4px; padding:6px 8px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff; cursor:pointer;">
                <Icon name="edit" :size="14" color="#fff" />
                Edit
              </button>
              <button @click="del(p)" style="display:flex; align-items:center; gap:4px; padding:6px 8px; border:1px solid #dc2626; border-radius:6px; background:#dc2626; color:#fff; cursor:pointer;">
                <Icon name="trash" :size="14" color="#fff" />
                Delete
              </button>
            </div>
          </td>
        </tr>
        <tr v-if="policies.length === 0">
          <td colspan="7" style="text-align:center; padding:40px; color:#6b7280; font-style:italic;">
            No policies configured yet. Click "Add Policy" to create your first permission rule.
          </td>
        </tr>
      </tbody>
    </table>
    <!-- Policy Modal -->
    <PolicyModal 
      v-model="showAdd" 
      mode="add" 
      :users="users" 
      :groups="groups" 
      :sources="sources" 
      @submit="create"
    />
    
    <PolicyModal 
      v-model="showEdit" 
      mode="edit" 
      :policy="editingPolicy" 
      :users="users" 
      :groups="groups" 
      :sources="sources" 
      @submit="updatePolicy"
    />
    <ToastContainer />
  </section>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'
import { useToasts } from '@/lib/toasts'
import ToastContainer from '@/components/ToastContainer.vue'
import PolicyModal from '@/components/PolicyModal.vue'
import Icon from '@/components/Icon.vue'

const policies = ref<any[]>([])
const users = ref<{sub:string, email:string, name?:string}[]>([])
const groups = ref<{id:number, name:string}[]>([])
const sources = ref<{id:number, name:string}[]>([])

const showAdd = ref(false)
const showEdit = ref(false)
const editingPolicy = ref<any>(null)

function sourceName(id:number|string){
  if (id === '*') return 'All Sources'
  const s = sources.value.find(s=>s.id===id); return s? s.name : id
}

async function load(){
  const r1 = await authenticatedApiRequest('/v1/admin/policies')
  if (r1.ok) policies.value = await r1.json()
  const r2 = await authenticatedApiRequest('/v1/sources')
  if (r2.ok) sources.value = await r2.json()
  const r3 = await authenticatedApiRequest('/v1/admin/users')
  if (r3.ok) users.value = await r3.json()
  const r4 = await authenticatedApiRequest('/v1/admin/groups')
  if (r4.ok) groups.value = await r4.json()

}

const toasts = useToasts()
async function create(formData: any){
  const res = await authenticatedApiRequest('/v1/admin/policies', { 
    method:'POST', 
    headers: { 'Content-Type':'application/json' },
    body: JSON.stringify(formData) 
  })
  if (res.ok) { 
    showAdd.value = false
    load(); toasts.push({ kind:'success', text:'Policy added' }) 
  } else toasts.push({ kind:'error', text:'Add failed' })
}
function editPolicy(policy: any) {
  editingPolicy.value = { ...policy }
  showEdit.value = true
}

async function updatePolicy(formData: any) {
  const res = await authenticatedApiRequest(`/v1/admin/policies/${editingPolicy.value.id}`, { 
    method: 'PUT', 
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(formData) 
  })
  if (res.ok) {
    showEdit.value = false
    editingPolicy.value = null
    load()
    toasts.push({ kind: 'success', text: 'Policy updated' })
  } else {
    toasts.push({ kind: 'error', text: 'Update failed' })
  }
}

async function del(p:any){
  if (!confirm('Delete policy?')) return
  const res = await authenticatedApiRequest(`/v1/admin/policies/${p.id}`, { method:'DELETE' })
  if (res.ok) { load(); toasts.push({ kind:'success', text:'Policy deleted' }) } else toasts.push({ kind:'error', text:'Delete failed' })
}

onMounted(load)
</script>
