<template>
  <div class="admin-roles">
    <div style="display:flex; align-items:center; justify-content:space-between; margin:0 0 10px 0;">
      <h2 style="margin:0;">Role Management</h2>
      <button @click="addRole" style="padding:8px 12px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff;">Add Role</button>
    </div>

    <div class="table-container">
      <table class="admin-table">
        <thead>
          <tr>
            <th>Role Name</th>
            <th>Description</th>
            <th>Permissions</th>
            <th>Users</th>
            <th style="text-align:center;">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="role in roles" :key="role.id">
            <td>{{ role.name }}</td>
            <td>{{ role.description || '-' }}</td>
            <td>
              <span style="padding:2px 8px; border-radius:12px; font-size:12px; background:#f3f4f6; color:#374151;">
                {{ getPermissions(role) }} permissions
              </span>
            </td>
            <td>
              <span style="padding:2px 8px; border-radius:12px; font-size:12px; background:#f3f4f6; color:#374151;">
                {{ getUserCount(role.id) }} users
              </span>
            </td>
            <td style="text-align:center;">
              <div style="display:flex; gap:8px; justify-content:center;">
                <button @click="managePermissions(role)" style="padding:6px; border:1px solid #059669; border-radius:4px; background:#d1fae5; cursor:pointer; display:flex; align-items:center;" title="Manage Permissions">
                  <Icon name="shield-check" :size="16" color="#059669" />
                </button>
                <button @click="editRole(role)" style="padding:6px; border:1px solid #3b82f6; border-radius:4px; background:#dbeafe; cursor:pointer; display:flex; align-items:center;" title="Edit">
                  <Icon name="edit" :size="16" color="#2563eb" />
                </button>
                <button @click="deleteRole(role)" style="padding:6px; border:1px solid #dc2626; border-radius:4px; background:#fee2e2; cursor:pointer; display:flex; align-items:center;" title="Delete" :disabled="getUserCount(role.id) > 0 || !!role.is_builtin">
                  <Icon name="trash" :size="16" color="#dc2626" />
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="roles.length === 0">
            <td colspan="5" style="text-align:center; padding:40px; color:#6b7280; font-style:italic;">
              No roles configured yet. Click "Add Role" to create your first role.
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Role Permission Modal -->
    <RolePermissionModal
      :show="showPermissionModal"
      :role="managingPermissionsRole"
      @close="handlePermissionModalClose"
      @saved="handlePermissionsSaved"
    />

    <ToastContainer />

     <!-- Simple Role Modal -->
      <div v-if="showAddModal || showEditModal" style="position:fixed; inset:0; background:rgba(0,0,0,0.5); display:flex; align-items:center; justify-content:center; z-index:1000;">
        <div style="background:white; padding:24px; border-radius:8px; width:500px; max-width:90vw;">
          <h3 style="margin:0 0 16px 0;">{{ showAddModal ? 'Add Role' : 'Edit Role' }}</h3>
          
          <!-- Built-in role warning -->
          <div v-if="showEditModal && selectedRole?.is_builtin === 1" style="background:#fef3c7; border:1px solid #f59e0b; border-radius:4px; padding:12px; margin-bottom:16px;">
            <div style="display:flex; align-items:center; gap:8px;">
              <Icon name="alert-triangle" style="color:#f59e0b; width:16px; height:16px;" />
              <span style="font-size:14px; color:#92400e; font-weight:500;">Built-in Role</span>
            </div>
            <p style="margin:4px 0 0 24px; font-size:13px; color:#92400e;">This is a built-in role. Only the description can be modified.</p>
          </div>
          
          <div style="margin-bottom:16px;">
            <label style="display:block; margin-bottom:4px; font-weight:500;">Role Name</label>
            <input 
              v-model="roleForm.name" 
              :disabled="showEditModal && selectedRole?.is_builtin === 1"
              style="width:100%; padding:8px; border:1px solid #ccc; border-radius:4px;" 
              :style="showEditModal && selectedRole?.is_builtin === 1 ? 'background-color: #f5f5f5; cursor: not-allowed;' : ''"
              placeholder="Enter role name" 
            />
            <div v-if="showEditModal && selectedRole?.is_builtin === 1" style="font-size:12px; color:#666; margin-top:4px;">
              Built-in role names cannot be modified
            </div>
          </div>
          
          <div style="margin-bottom:16px;">
            <label style="display:block; margin-bottom:4px; font-weight:500;">Description</label>
            <textarea v-model="roleForm.description" style="width:100%; padding:8px; border:1px solid #ccc; border-radius:4px; height:80px;" placeholder="Enter description (optional)"></textarea>
          </div>
          
          <div style="display:flex; gap:8px; justify-content:flex-end;">
            <button @click="cancelModal" style="padding:8px 16px; border:1px solid #ccc; background:white; border-radius:4px; cursor:pointer;">Cancel</button>
            <button @click="submitModal" style="padding:8px 16px; border:none; background:#3b82f6; color:white; border-radius:4px; cursor:pointer;">{{ showAddModal ? 'Create' : 'Update' }}</button>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'
import { useToasts } from '@/lib/toasts'
import ToastContainer from '@/components/ToastContainer.vue'
import Icon from '@/components/Icon.vue'
import RolePermissionModal from '@/components/RolePermissionModal.vue'
import type { Role, RoleFormData } from '@/types/admin-modals'

const roles = ref<Role[]>([])
const showAddModal = ref(false)
const showEditModal = ref(false)
const showPermissionModal = ref(false)
const selectedRole = ref<Role | null>(null)
const managingPermissionsRole = ref<Role | null>(null)
const roleForm = ref<RoleFormData>({
  name: '',
  description: '',
  permissions: []
})
const toasts = useToasts()

async function load() {
  try {
    const res = await authenticatedApiRequest('/v1/admin/roles')
    if (res.ok) {
      roles.value = await res.json()
    } else {
      toasts.push({ kind: 'error', text: 'Failed to load roles' })
    }
  } catch (error) {
    toasts.push({ kind: 'error', text: 'Error loading roles' })
  }
}

// Helper functions
function getPermissions(role: Role): number {
  if (role.default_actions) {
    try {
      const actions = typeof role.default_actions === 'string' 
        ? JSON.parse(role.default_actions) 
        : role.default_actions
      return Array.isArray(actions) ? actions.length : Object.keys(actions).length
    } catch {
      return 0
    }
  }
  return role.permissions?.length || 0
}

function getUserCount(roleId: number): number {
  // This would typically come from the API response
  // For now, return 0 as placeholder - should be populated by backend
  return 0
}

function formatDate(dateString: string) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString()
}

function addRole() {
  showAddModal.value = true
}

function editRole(role: Role) {
  selectedRole.value = role
  roleForm.value = {
    name: role.name,
    description: role.description || '',
    permissions: role.permissions || []
  }
  showEditModal.value = true
}

function managePermissions(role: Role) {
  managingPermissionsRole.value = role
  showPermissionModal.value = true
}

async function handleAddRole(formData: RoleFormData) {
  try {
    const res = await authenticatedApiRequest('/v1/admin/roles', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: formData.name,
        description: formData.description,
        default_actions: formData.permissions || []
      })
    })
    
    if (res.ok) {
      toasts.push({ kind: 'success', text: 'Role created successfully' })
      load()
    } else {
      const errorData = await res.json().catch(() => ({}))
      toasts.push({ kind: 'error', text: errorData.error?.message || 'Failed to create role' })
    }
  } catch (error) {
    toasts.push({ kind: 'error', text: 'Error creating role' })
  }
}

async function handleEditRole(formData: RoleFormData) {
  if (!selectedRole.value) return
  
  try {
    const res = await authenticatedApiRequest(`/v1/admin/roles/${selectedRole.value.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: formData.name,
        description: formData.description,
        default_actions: formData.permissions || []
      })
    })
    
    if (res.ok) {
      toasts.push({ kind: 'success', text: 'Role updated successfully' })
      load()
    } else {
      const errorData = await res.json().catch(() => ({}))
      if (errorData.error?.code === 'BUILTIN_ROLE_NAME_CANNOT_BE_MODIFIED') {
        toasts.push({ kind: 'error', text: 'Built-in role names cannot be modified' })
      } else {
        toasts.push({ kind: 'error', text: errorData.error?.message || 'Failed to update role' })
      }
    }
  } catch (error) {
    toasts.push({ kind: 'error', text: 'Error updating role' })
  }
}

function cancelModal() {
  showAddModal.value = false
  showEditModal.value = false
  roleForm.value = {
    name: '',
    description: '',
    permissions: []
  }
}

function handlePermissionModalClose() {
  showPermissionModal.value = false
  managingPermissionsRole.value = null
}

function handlePermissionsSaved() {
  // Optionally reload roles if needed
  load()
}

function submitModal() {
  if (showAddModal.value) {
    handleAddRole(roleForm.value)
  } else if (showEditModal.value) {
    handleEditRole(roleForm.value)
  }
  cancelModal()
}

async function deleteRole(role: Role) {
  if (role.is_builtin === 1) {
    toasts.push({ kind: 'error', text: 'Built-in roles cannot be deleted' })
    return
  }
  
  if ((role.user_count || 0) > 0) {
    toasts.push({ kind: 'error', text: 'Cannot delete role with assigned users' })
    return
  }
  
  if (!confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
    return
  }
  
  try {
    const res = await authenticatedApiRequest(`/v1/admin/roles/${role.id}`, {
      method: 'DELETE'
    })
    
    if (res.ok) {
      toasts.push({ kind: 'success', text: 'Role deleted successfully' })
      load()
    } else {
      const errorData = await res.json().catch(() => ({}))
      if (errorData.error?.code === 'BUILTIN_ROLE_CANNOT_BE_DELETED') {
        toasts.push({ kind: 'error', text: 'Built-in roles cannot be deleted' })
      } else {
        toasts.push({ kind: 'error', text: 'Failed to delete role' })
      }
    }
  } catch (error) {
    toasts.push({ kind: 'error', text: 'Error deleting role' })
  }
}

onMounted(load)
</script>