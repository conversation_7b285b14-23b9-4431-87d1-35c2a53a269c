<template>
  <section>
    <h1 style="margin:0 0 12px 0;">Administration</h1>
    <div style="display:flex; gap:12px; flex-wrap:wrap;">
      <router-link to="/admin/sources" class="card">Sources</router-link>
    <router-link to="/admin/users" class="card">Users</router-link>
    <router-link to="/admin/groups" class="card">Groups</router-link>
    <router-link to="/admin/roles" class="card">Roles</router-link>
     <router-link to="/admin/permissions" class="card">Permissions</router-link>
     <router-link to="/admin/policies" class="card">Policies</router-link>
    </div>
  </section>
</template>

<style scoped>
.card { display:flex; align-items:center; justify-content:center; width:200px; height:100px; border:1px solid #e5e7eb; border-radius:8px; background:#fff; text-decoration:none; color:#111827; font-weight:600; }
.card:hover { box-shadow: 0 2px 8px rgba(0,0,0,0.08); border-color:#cbd5e1; }
</style>

