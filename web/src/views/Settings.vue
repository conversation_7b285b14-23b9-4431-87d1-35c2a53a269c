<template>
  <section style="max-width:720px; margin:16px auto;">
    <h1 style="margin:0 0 12px 0;">Settings</h1>
    <div style="border:1px solid #e5e7eb; border-radius:8px; overflow:hidden; background:#fff;">
      <div style="padding:12px; border-bottom:1px solid #e5e7eb; font-weight:600;">Folder Options</div>
      <div style="padding:12px; display:flex; flex-direction:column; gap:12px;">
        <label style="display:flex; align-items:center; gap:8px;">
          <input type="checkbox" v-model="prefs.hideHidden" />
          Hide hidden files (.dotfiles)
        </label>
        <label style="display:flex; align-items:center; gap:8px;">
          <input type="checkbox" v-model="prefs.foldersFirst" />
          Show folders first when sorting by name
        </label>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { usePrefs } from '@/lib/prefs'
const prefs = usePrefs()
</script>

