<template>
  <section>
    <h1 style="margin: 0 0 0.5rem 0;">Upload</h1>
    <p style="margin: 0 0 1rem 0; color: #555;">Choose FS (direct) or S3 (multipart)</p>

    <div style="display:flex; gap:1rem; align-items:center; margin-bottom: 0.5rem;">
      <label><input type="radio" value="fs" v-model="provider" /> FS</label>
      <label><input type="radio" value="s3" v-model="provider" /> S3</label>
    </div>

    <div style="display:flex; gap:0.5rem; align-items:center; margin-bottom: 0.5rem;">
      <input type="text" v-model="destPath" placeholder="/path/filename.ext" style="flex:1; padding:0.5rem;" />
      <input type="file" @change="onPick" />
    </div>

    <div style="display:flex; gap:0.5rem; align-items:center; margin-bottom: 1rem;">
      <label v-if="provider==='fs'"><input type="checkbox" v-model="overwrite" /> Overwrite</label>
      <template v-else>
        <label>Part size (MB): <input type="number" min="5" max="128" v-model.number="partSizeMB" style="width:5rem; padding:0.25rem;"/></label>
        <label>Retries: <input type="number" min="1" max="5" v-model.number="maxRetries" style="width:4rem; padding:0.25rem;"/></label>
      </template>
      <button :disabled="!file || !destPath || busy" @click="startUpload" style="padding:0.5rem 0.75rem;">{{ busy ? 'Uploading…' : 'Upload' }}</button>
    </div>

    <div v-if="progress.total">
      <div style="height: 8px; background:#eee; width: 100%;">
        <div :style="{height:'8px', background:'#4f46e5', width: pct + '%'}"></div>
      </div>
      <p style="margin:0.5rem 0 0 0; color:#333;">{{ progress.sent }} / {{ progress.total }} bytes ({{ pct.toFixed(0) }}%)</p>
    </div>

    <p v-if="message" style="margin-top:0.5rem; color:#006400;">{{ message }}</p>
    <p v-if="error" style="margin-top:0.5rem; color:#8b0000;">{{ error }}</p>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { uploadFileFS } from '@/lib/fsUpload'
import { uploadFileMultipart } from '@/lib/multipart'

const provider = ref<'fs'|'s3'>('fs')
const destPath = ref('')
const overwrite = ref(false)
const file = ref<File|null>(null)
const busy = ref(false)
const message = ref('')
const error = ref('')
const progress = ref({ sent: 0, total: 0 })
const pct = computed(() => progress.value.total ? (progress.value.sent / progress.value.total) * 100 : 0)
const partSizeMB = ref(5)
const maxRetries = ref(3)

function onPick(e: Event) {
  const input = e.target as HTMLInputElement
  file.value = input.files && input.files[0] ? input.files[0] : null
}

async function startUpload() {
  if (!file.value || !destPath.value) return
  busy.value = true
  message.value = ''
  error.value = ''
  progress.value = { sent: 0, total: file.value.size }
  try {
    if (provider.value === 'fs') {
      await uploadFileFS(file.value, destPath.value, overwrite.value, (chunk, total) => {
        progress.value = { sent: Math.min(progress.value.sent + chunk, total), total }
      })
    } else {
      await uploadFileMultipart(file.value, destPath.value, { partSizeMB: partSizeMB.value, maxRetries: maxRetries.value })
      progress.value = { sent: file.value.size, total: file.value.size }
    }
    message.value = 'Upload complete'
  } catch (e: any) {
    error.value = e?.message || 'Upload failed'
  } finally {
    busy.value = false
  }
}
</script>
