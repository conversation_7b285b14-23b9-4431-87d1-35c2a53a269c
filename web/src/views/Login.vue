<template>
  <section style="max-width:420px; margin:40px auto; padding:20px; border:1px solid #e5e7eb; border-radius:8px; background:#fff; text-align:center;">
    <img src="/logo.png" alt="Logo" style="height:48px; margin-bottom:12px;"/>
    <h1 style="margin:0 0 12px 0;">Sign in</h1>
    <p style="margin:0 0 12px 0; color:#4b5563;">Use your Google account to continue.</p>
    
    <!-- Error message -->
    <div v-if="errorMessage" style="margin:0 0 16px 0; padding:12px; background:#fee2e2; border:1px solid #fca5a5; border-radius:6px; color:#dc2626; text-align:left;">
      {{ errorMessage }}
    </div>
    
    <div id="g_id_onload"
         :data-client_id="clientId"
         data-context="signin"
         data-ux_mode="popup"
         data-callback="handleGoogleCredential"></div>
    <div class="g_id_signin" data-type="standard" data-size="large" data-theme="outline" data-text="signin_with" data-shape="rect" data-logo_alignment="left"></div>
  </section>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { loginWithGoogleIdToken } from '@/lib/auth'
const clientId = '**********-0ddpukpl3kqiuagcq8a8fm7rjmvqvnsi.apps.googleusercontent.com'

const router = useRouter()

const errorMessage = ref('')

// Expose a global callback for GIS
;(window as any).handleGoogleCredential = async (resp: any) => {
  try {
    errorMessage.value = '' // Clear any previous errors
    const idToken = resp.credential
    await loginWithGoogleIdToken(idToken)
    // Use Vue Router navigation instead of window.location to avoid race condition
    await router.push('/')
  } catch (e: any) {
    errorMessage.value = e.message || 'Login failed'
  }
}

onMounted(() => {
  // Ensure script present
  if (!document.getElementById('google-identity')) {
    const s = document.createElement('script')
    s.src = 'https://accounts.google.com/gsi/client'
    s.async = true
    s.defer = true
    s.id = 'google-identity'
    document.head.appendChild(s)
  }
})
</script>
