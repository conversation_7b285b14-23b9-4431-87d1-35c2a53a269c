import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// Clear authentication and recent sources on startup
async function initializeApp() {
  const { clearToken } = await import('./lib/auth')
  clearToken()

  // Clear recent sources from localStorage
  const KEY = 'fb_prefs'
  try {
    const currentPrefs = localStorage.getItem(KEY)
    if (currentPrefs) {
      const prefs = JSON.parse(currentPrefs)
      prefs.recentSources = []
      localStorage.setItem(KEY, JSON.stringify(prefs))
    }
  } catch (error) {
    // If parsing fails, reset to defaults
    const defaultPrefs = {
      foldersFirst: true,
      hideHidden: true,
      recentSources: []
    }
    localStorage.setItem(KEY, JSON.stringify(defaultPrefs))
  }

  const app = createApp(App)
  app.use(createPinia())
  app.use(router)
  app.mount('#app')
}

// Initialize the application
initializeApp()

