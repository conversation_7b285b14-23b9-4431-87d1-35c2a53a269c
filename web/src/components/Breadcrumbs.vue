<template>
  <nav aria-label="Breadcrumb" style="display:flex; gap:0.25rem; align-items:center; flex-wrap: wrap;">
    <template v-for="(seg, i) in segments" :key="seg.path">
      <span style="color:#aaa;">/</span>
      <a href="#" @click.prevent="go(seg.path)" style="text-decoration:none; color:#2563eb;">{{ seg.label }}</a>
    </template>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{ path: string }>()
const emit = defineEmits<{ (e: 'navigate', path: string): void }>()

const segments = computed(() => {
  const p = (props.path || '/').replace(/\/+$/, '')
  if (p === '' || p === '/') return []
  const parts = p.split('/').filter(Boolean)
  let acc = ''
  return parts.map((label) => {
    acc += '/' + label
    return { label, path: acc }
  })
})

function go(path: string) {
  emit('navigate', path)
}
</script>
