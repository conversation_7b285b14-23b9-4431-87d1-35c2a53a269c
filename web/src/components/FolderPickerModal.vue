<template>
  <div v-if="modelValue" style="position:fixed; inset:0; background:rgba(0,0,0,0.5); display:flex; align-items:flex-start; justify-content:center; z-index:9999; padding-top:20px;">
    <div style="width:min(92vw, 600px); background:#fff; border:1px solid #e5e7eb; border-radius:12px; box-shadow:0 12px 28px rgba(0,0,0,0.12); overflow:hidden;">
      <div style="display:flex; align-items:center; justify-content:space-between; padding:12px; border-bottom:1px solid #e5e7eb;">
        <div style="font-weight:700;">Select Folder</div>
        <button @click="$emit('update:modelValue', false)" style="padding:6px 10px; border:1px solid #e5e7eb; border-radius:6px; background:#fff; cursor:pointer;">✖️</button>
      </div>
      <FolderPicker 
        :initial-path="initialPath"
        @select="onFolderSelected"
        @cancel="$emit('update:modelValue', false)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import FolderPicker from './FolderPicker.vue'

interface Props {
  modelValue: boolean
  initialPath?: string
}

interface Emits {
  'update:modelValue': [value: boolean]
  'select': [path: string]
}

defineProps<Props>()
const emit = defineEmits<Emits>()

function onFolderSelected(path: string) {
  emit('select', path)
  emit('update:modelValue', false)
}
</script>