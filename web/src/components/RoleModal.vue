<template>
  <Modal v-model="show" :title="modalTitle" :width="500">
    <div :style="MODAL_STYLES.container">
      <!-- Role Name Field -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Role Name *</label>
        <input 
          v-model="formData.name" 
          placeholder="Enter role name" 
          :style="Object.assign({}, MODAL_STYLES.input, { borderColor: errors.name ? '#dc2626' : '#e5e7eb' })"
          @blur="validateForm"
        />
        <small v-if="errors.name" :style="MODAL_STYLES.error">{{ errors.name }}</small>
      </div>

      <!-- Description Field -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Description</label>
        <textarea 
          v-model="formData.description" 
          placeholder="Enter role description (optional)" 
          :style="Object.assign({}, MODAL_STYLES.textarea, { borderColor: errors.description ? '#dc2626' : '#e5e7eb' })"
          @blur="validateForm"
        ></textarea>
        <small v-if="errors.description" :style="MODAL_STYLES.error">{{ errors.description }}</small>
      </div>

      <!-- Permissions Field -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Permissions</label>
        <div style="display:flex; flex-direction:column; gap:8px; border:1px solid #e5e7eb; border-radius:6px; padding:12px; background:#f9fafb;">
          <div v-for="permission in availablePermissions" :key="permission.value" :style="MODAL_STYLES.checkbox">
            <input 
              type="checkbox" 
              :value="permission.value"
              v-model="formData.permissions"
            /> 
            <span style="font-weight:500;">{{ permission.label }}</span>
            <small style="color:#6b7280; margin-left:auto;">{{ permission.description }}</small>
          </div>
          <div v-if="availablePermissions.length === 0" style="color:#6b7280; font-style:italic; text-align:center;">
            No permissions available
          </div>
        </div>
        <small v-if="errors.permissions" :style="MODAL_STYLES.error">{{ errors.permissions }}</small>
      </div>

      <!-- Edit Mode: Additional Info -->
      <div v-if="mode === 'edit' && role" :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Role Information</label>
        <div style="display:flex; gap:8px; align-items:center;">
          <span style="padding:4px 8px; border-radius:12px; font-size:12px; font-weight:500; background:#f3f4f6; color:#374151; border:1px solid #9ca3af;">
            {{ role.user_count || 0 }} users assigned
          </span>
          <span v-if="role.created_at" style="color:#6b7280; font-size:12px;">
            Created {{ formatDate(role.created_at) }}
          </span>
        </div>
      </div>
    </div>

    <template #footer>
      <div :style="MODAL_STYLES.footer">
        <button 
          @click="cancel" 
          :style="MODAL_STYLES.buttonCancel"
          :disabled="isSubmitting"
        >
          Cancel
        </button>
        <button 
          @click="submit" 
          :disabled="!isValid || isSubmitting" 
          :style="`${MODAL_STYLES.buttonPrimary} opacity:${(isValid && !isSubmitting) ? 1 : 0.6};`"
        >
          {{ isSubmitting ? 'Saving...' : (mode === 'add' ? 'Create Role' : 'Update Role') }}
        </button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import Modal from './Modal.vue'
import type { RoleModalProps, RoleModalEmits, RoleFormData, ValidationErrors } from '@/types/admin-modals'
import { MODAL_STYLES } from '@/types/admin-modals'
import { useBaseModal } from '@/composables/useBaseModal'

const props = withDefaults(defineProps<RoleModalProps>(), {
  mode: 'add'
})

const emit = defineEmits<RoleModalEmits>()

const modalTitle = computed(() => 
  props.mode === 'add' ? 'Create Role' : 'Edit Role'
)

// Available permissions - this could be fetched from API
const availablePermissions = [
  { value: 'read', label: 'Read', description: 'View files and folders' },
  { value: 'write', label: 'Write', description: 'Create and modify files' },
  { value: 'delete', label: 'Delete', description: 'Remove files and folders' },
  { value: 'admin', label: 'Admin', description: 'Full administrative access' },
  { value: 'upload', label: 'Upload', description: 'Upload new files' },
  { value: 'download', label: 'Download', description: 'Download files' }
]

function formatDate(dateString: string) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString()
}

// Validation function
const validateRoleData = (data: RoleFormData): Record<string, string> => {
  const errs: Record<string, string> = {}
  
  if (!data.name?.trim()) {
    errs.name = 'Role name is required'
  } else if (data.name.trim().length < 2) {
    errs.name = 'Role name must be at least 2 characters long'
  } else if (data.name.trim().length > 50) {
    errs.name = 'Role name must be less than 50 characters'
  }
  
  if (data.description && data.description.trim().length > 200) {
    errs.description = 'Description must be less than 200 characters'
  }
  
  return errs
}

// Load role data function
const loadRoleData = () => {
  if (props.mode === 'edit' && props.role) {
    formData.value = {
      name: props.role.name,
      description: props.role.description || '',
      permissions: props.role.permissions || []
    }
  }
}

// Use base modal composable
const {
  show,
  formData,
  errors,
  isSubmitting,
  isValid,
  validateForm,
  cancel,
  submit
} = useBaseModal<RoleFormData>(props, emit, {
  initialData: () => ({
    name: '',
    description: '',
    permissions: []
  }),
  validate: validateRoleData,
  onLoadData: loadRoleData
})

// Watch for prop changes to reload data
watch([() => props.mode, () => props.role], loadRoleData, { immediate: true })
</script>