<template>
  <Modal v-model="show" :title="modalTitle" :width="600">
    <div :style="MODAL_STYLES.container">
      <!-- Group Name Field -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Group Name *</label>
        <input 
          v-model="formData.name" 
          placeholder="Enter group name" 
          :style="Object.assign({}, MODAL_STYLES.input, { borderColor: errors.name ? '#dc2626' : '#e5e7eb' })"
          @blur="validateForm"
        />
        <small v-if="errors.name" :style="MODAL_STYLES.error">{{ errors.name }}</small>
      </div>

      <!-- Members Management (Edit Mode) -->
      <div v-if="mode === 'edit' && users" :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Group Members</label>
        
        <!-- Add Member Section -->
        <div style="display:flex; gap:8px; align-items:flex-end;">
          <div style="flex:1;">
            <select 
              v-model="selectedUserToAdd" 
              :style="MODAL_STYLES.select"
            >
              <option value="">Select a user to add...</option>
              <option 
                v-for="user in availableUsers" 
                :key="user.sub" 
                :value="user.sub"
              >
                {{ user.email }} {{ user.name ? `(${user.name})` : '' }}
              </option>
            </select>
          </div>
          <button 
            @click="addMember" 
            :disabled="!selectedUserToAdd"
            :style="`${MODAL_STYLES.buttonPrimary} opacity:${selectedUserToAdd ? 1 : 0.6};`"
          >
            Add Member
          </button>
        </div>

        <!-- Current Members List -->
        <div v-if="formData.members && formData.members.length > 0" style="margin-top:12px;">
          <div style="font-size:13px; color:#6b7280; margin-bottom:8px;">Current Members:</div>
          <div style="display:flex; flex-direction:column; gap:4px;">
            <div 
              v-for="memberSub in formData.members" 
              :key="memberSub"
              style="display:flex; justify-content:space-between; align-items:center; padding:8px; background:#f9fafb; border:1px solid #e5e7eb; border-radius:6px;"
            >
              <span style="font-size:14px;">{{ getUserEmail(memberSub) }}</span>
              <button 
                @click="removeMember(memberSub)"
                style="padding:4px 8px; border:1px solid #fca5a5; border-radius:4px; background:#fee2e2; color:#dc2626; cursor:pointer; font-size:12px;"
                title="Remove member"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
        
        <div v-else style="padding:16px; text-align:center; color:#6b7280; font-style:italic; background:#f9fafb; border:1px solid #e5e7eb; border-radius:6px; margin-top:8px;">
          No members in this group yet
        </div>
      </div>

      <!-- Add Mode: Members Note -->
      <div v-if="mode === 'add'" style="padding:12px; background:#f0f9ff; border:1px solid #0ea5e9; border-radius:6px;">
        <div style="font-size:14px; color:#0c4a6e; font-weight:500; margin-bottom:4px;">Note:</div>
        <div style="font-size:13px; color:#0c4a6e;">
          You can add members to this group after it's created.
        </div>
      </div>
    </div>

    <template #footer>
      <div :style="MODAL_STYLES.footer">
        <button 
          @click="cancel" 
          :style="MODAL_STYLES.buttonCancel"
          :disabled="isSubmitting"
        >
          Cancel
        </button>
        <button 
          @click="submit" 
          :disabled="!isValid || isSubmitting" 
          :style="Object.assign({}, MODAL_STYLES.buttonPrimary, { opacity: (isValid && !isSubmitting) ? 1 : 0.6 })"
        >
          {{ isSubmitting ? 'Saving...' : (mode === 'add' ? 'Create Group' : 'Update Group') }}
        </button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Modal from './Modal.vue'
import type { 
  GroupModalProps, 
  GroupModalEmits, 
  GroupFormData, 
  User 
} from '@/types/admin-modals'
import { MODAL_STYLES } from '@/types/admin-modals'
import { useBaseModal } from '@/composables/useBaseModal'

const props = withDefaults(defineProps<GroupModalProps>(), {
  mode: 'add'
})

const emit = defineEmits<GroupModalEmits>()

const modalTitle = computed(() => 
  props.mode === 'add' ? 'Create Group' : 'Edit Group'
)

const selectedUserToAdd = ref('')

// Validation function
const validateGroupData = (data: GroupFormData): Record<string, string> => {
  const errs: Record<string, string> = {}
  
  if (!data.name?.trim()) {
    errs.name = 'Group name is required'
  } else if (data.name.trim().length < 2) {
    errs.name = 'Group name must be at least 2 characters long'
  }
  
  return errs
}

// Load group data function
const loadGroupData = () => {
  if (props.mode === 'edit' && props.group) {
    formData.value = {
      name: props.group.name,
      members: props.group.members ? [...props.group.members] : []
    }
  }
  selectedUserToAdd.value = ''
}

// Use base modal composable
const {
  show,
  formData,
  errors,
  isSubmitting,
  isValid,
  validateForm,
  cancel,
  submit
} = useBaseModal<GroupFormData>(props, emit, {
  initialData: () => ({
    name: '',
    members: []
  }),
  validate: validateGroupData,
  onLoadData: loadGroupData,
  onReset: () => {
    selectedUserToAdd.value = ''
  }
})

const availableUsers = computed(() => {
  if (!props.users) return []
  return props.users.filter(user => 
    !formData.value.members?.includes(user.sub)
  )
})

function getUserEmail(sub: string): string {
  if (!props.users) return sub
  const user = props.users.find(u => u.sub === sub)
  return user ? `${user.email}${user.name ? ` (${user.name})` : ''}` : sub
}

function addMember() {
  if (!selectedUserToAdd.value) return
  
  if (!formData.value.members) {
    formData.value.members = []
  }
  
  if (!formData.value.members.includes(selectedUserToAdd.value)) {
    formData.value.members.push(selectedUserToAdd.value)
  }
  
  selectedUserToAdd.value = ''
}

function removeMember(sub: string) {
  if (!formData.value.members) return
  
  const index = formData.value.members.indexOf(sub)
  if (index > -1) {
    formData.value.members.splice(index, 1)
  }
}

// Watch for prop changes to reload data
watch([() => props.mode, () => props.group], loadGroupData, { immediate: true })
</script>