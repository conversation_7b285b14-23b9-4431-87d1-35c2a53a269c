<template>
  <div v-if="modelValue" style="position:fixed; inset:0; background:rgba(0,0,0,0.35); display:flex; align-items:center; justify-content:center; z-index:1100;" @keydown.esc="$emit('update:modelValue', false)" tabindex="-1">
    <div :style="panelStyle">
      <div style="display:flex; align-items:center; justify-content:space-between; margin:-16px -16px 12px -16px; padding:12px 16px; background:#dbeafe; border-bottom:2px solid #93c5fd; border-radius:10px 10px 0 0;">
        <h3 style="margin:0; font-size:16px; font-weight:600; color:#0f172a;">{{ title }}</h3>
        <button @click="$emit('update:modelValue', false)" style="border:0; background:transparent; font-size:20px; cursor:pointer; color:#0f172a; padding:4px; border-radius:4px;" onmouseover="this.style.background='#bfdbfe'" onmouseout="this.style.background='transparent'">×</button>
      </div>
      <div>
        <slot />
      </div>
      <div v-if="$slots.footer" style="margin-top:12px; display:flex; gap:8px; justify-content:flex-end;">
        <slot name="footer" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue'

const props = withDefaults(defineProps<{ modelValue: boolean; title: string; width?: number }>(), { width: 820 })
const emit = defineEmits(['update:modelValue'])
const panelStyle = `width:${props.width}px; max-width:95vw; border:1px solid #e5e7eb; border-radius:10px; padding:16px; background:#fff; box-shadow:0 10px 30px rgba(0,0,0,0.2);`

const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.modelValue) {
    emit('update:modelValue', false)
  }
}

watch(() => props.modelValue, (isOpen) => {
  if (isOpen) {
    document.addEventListener('keydown', handleEscape)
  } else {
    document.removeEventListener('keydown', handleEscape)
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
})
</script>

