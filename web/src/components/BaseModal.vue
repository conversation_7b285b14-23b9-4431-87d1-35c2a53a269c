<template>
  <Modal v-model="show" :title="modalTitle" :width="width">
    <div :style="MODAL_STYLES.container">
      <slot :formData="formData" :errors="errors" :isSubmitting="isSubmitting" :validateForm="validateForm" />
    </div>
    
    <template #footer>
      <button 
        @click="cancel" 
        :style="MODAL_STYLES.buttonCancel"
        :disabled="isSubmitting"
      >
        Cancel
      </button>
      <button 
        @click="submit" 
        :style="`${MODAL_STYLES.buttonPrimary} opacity:${(isValid && !isSubmitting) ? 1 : 0.6}; cursor: ${(isValid && !isSubmitting) ? 'pointer' : 'not-allowed'};`"
        :disabled="!isValid || isSubmitting"
      >
        {{ isSubmitting ? 'Saving...' : (mode === 'add' ? 'Create' : 'Update') }}
      </button>
    </template>
  </Modal>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
import { computed, ref, watch } from 'vue'
import Modal from './Modal.vue'
import { MODAL_STYLES } from '@/types/admin-modals'

interface BaseModalProps {
  modelValue: boolean
  mode: 'add' | 'edit'
  width?: number
  addTitle: string
  editTitle: string
}

const props = withDefaults(defineProps<BaseModalProps>(), {
  width: 500
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: T]
  'cancel': []
}>()

// Core modal state
const show = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const modalTitle = computed(() => 
  props.mode === 'add' ? props.addTitle : props.editTitle
)

// Form state - to be managed by child components
const formData = ref<T>({} as T)
const errors = ref<Record<string, string>>({})
const isSubmitting = ref(false)
const isValid = ref(false)

// Core modal functions
function validateForm(): boolean {
  // Override this in child components
  return true
}

function reset(): void {
  // Override this in child components
  formData.value = {} as T
  errors.value = {}
  isSubmitting.value = false
}

function loadData(): void {
  // Override this in child components
}

function cancel() {
  reset()
  emit('cancel')
  show.value = false
}

async function submit() {
  if (!isValid.value || isSubmitting.value) return
  
  try {
    isSubmitting.value = true
    emit('submit', formData.value)
    reset()
    show.value = false
  } catch (error) {
    console.error('Error submitting form:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Expose functions for child components to override
defineExpose({
  formData,
  errors,
  isSubmitting,
  isValid,
  validateForm,
  reset,
  loadData
})

// Watch for modal state changes
watch(show, (newValue) => {
  if (newValue) {
    loadData()
  } else {
    reset()
  }
})
</script>