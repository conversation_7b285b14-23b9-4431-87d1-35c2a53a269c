<template>
  <div aria-live="polite" style="position:fixed; top:12px; right:12px; display:flex; flex-direction:column; gap:8px; z-index:9999;">
    <div
      v-for="t in toasts.state.items"
      :key="t.id"
      :style="boxStyle(t.kind)"
    >
      <span>{{ t.text }}</span>
      <button v-if="t.action" @click="onAction(t)" style="margin-left:8px; padding:2px 6px; background:#ffffff22; border:1px solid #ffffff55; border-radius:4px; color:inherit; cursor:pointer;">
        {{ t.actionLabel || 'Undo' }}
      </button>
      <button @click="toasts.dismiss(t.id)" style="margin-left:6px; padding:0 6px; background:transparent; border:0; color:inherit; cursor:pointer;">×</button>
    </div>
  </div>
  
</template>

<script setup lang="ts">
import { useToasts } from '@/lib/toasts'

const toasts = useToasts()

function boxStyle(kind?: 'info'|'success'|'error') {
  const base = 'box-shadow: 0 2px 8px rgba(0,0,0,0.12); border-radius:8px; padding:8px 10px; display:flex; align-items:center;'
  if (kind === 'success') return base + ' background:#065f46; color:#ecfdf5; border:1px solid #064e3b;'
  if (kind === 'error') return base + ' background:#7f1d1d; color:#fee2e2; border:1px solid #7f1d1d;'
  return base + ' background:#1e3a8a; color:#e0e7ff; border:1px solid #1e40af;'
}

async function onAction(t: any) {
  try {
    await t.action?.()
  } finally {
    toasts.dismiss(t.id)
  }
}
</script>

