<template>
  <div class="folder-picker">
    <div class="picker-header">
      <div class="breadcrumbs">
        <button @click="navigateTo('/')" class="breadcrumb-btn">🏠</button>
        <template v-for="(part, index) in pathParts" :key="index">
          <span class="separator">/</span>
          <button @click="navigateTo(getPathUpTo(index))" class="breadcrumb-btn">{{ part }}</button>
        </template>
      </div>
      <div class="current-path">
        <strong>Selected:</strong> {{ currentPath }}
      </div>
    </div>
    
    <div class="picker-body">
      <div v-if="loading" class="loading">Loading...</div>
      <div v-else-if="error" class="error">{{ error }}</div>
      <div v-else class="folder-list">
        <div v-if="currentPath !== '/'" @click="goUp" class="folder-item parent">
          📁 ..
        </div>
        <div 
          v-for="folder in folders" 
          :key="folder.path"
          @click="navigateTo(folder.path)"
          class="folder-item"
        >
          📁 {{ folder.name }}
        </div>
        <div v-if="folders.length === 0" class="no-folders">
          No subfolders found
        </div>
      </div>
    </div>
    
    <div class="picker-footer">
      <button @click="$emit('cancel')" class="btn btn-cancel">Cancel</button>
      <button @click="selectCurrent" class="btn btn-select">Select This Folder</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'

interface FolderItem {
  name: string
  path: string
  kind: 'dir'
}

const props = defineProps<{
  initialPath?: string
}>()

const emit = defineEmits<{
  select: [path: string]
  cancel: []
}>()

const currentPath = ref(props.initialPath || '/Users')
const folders = ref<FolderItem[]>([])
const loading = ref(false)
const error = ref('')

const pathParts = computed(() => {
  return currentPath.value.split('/').filter(Boolean)
})

function getPathUpTo(index: number): string {
  const parts = pathParts.value.slice(0, index + 1)
  return '/' + parts.join('/')
}

function goUp() {
  if (currentPath.value === '/') return
  const parts = currentPath.value.split('/').filter(Boolean)
  parts.pop()
  currentPath.value = '/' + parts.join('/')
  if (currentPath.value === '//') currentPath.value = '/'
}

function navigateTo(path: string) {
  currentPath.value = path
}

function selectCurrent() {
  emit('select', currentPath.value)
}

async function loadFolders() {
  loading.value = true
  error.value = ''
  
  try {
    // Try to get a filesystem source to use, but fallback to browse mode if none exists
    let sourceId = null
    
    try {
      const sourcesResponse = await authenticatedApiRequest('/v1/sources')
      
      if (sourcesResponse.ok) {
        const sources = await sourcesResponse.json()
        const fsSource = sources.find((s: any) => s.kind === 'fs')
        if (fsSource) {
          sourceId = fsSource.id.toString()
        }
      }
    } catch (e) {
      // Ignore source loading errors, we'll try browse mode
    }
    
    // Use the filesystem provider to list directories
    const params = new URLSearchParams({
      provider: 'fs',
      path: currentPath.value,
      page: '1',
      size: '1000'
    })
    
    if (sourceId) {
      params.set('sourceId', sourceId)
    }
    
    const response = await authenticatedApiRequest(`/v1/files?${params}`)
    
    if (!response.ok) {
      throw new Error(`Failed to load folders: ${response.statusText}`)
    }
    
    const data = await response.json()
    folders.value = data.items.filter((item: any) => item.kind === 'dir')
  } catch (e: any) {
    error.value = e.message || 'Failed to load folders'
    folders.value = []
  } finally {
    loading.value = false
  }
}

watch(currentPath, loadFolders)
onMounted(loadFolders)
</script>

<style scoped>
.folder-picker {
  display: flex;
  flex-direction: column;
  height: 400px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.picker-header {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.breadcrumbs {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.breadcrumb-btn {
  background: none;
  border: none;
  color: #1e40af;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 14px;
}

.breadcrumb-btn:hover {
  background: #e0e7ff;
}

.separator {
  color: #6b7280;
  font-size: 12px;
}

.current-path {
  font-size: 12px;
  color: #6b7280;
}

.picker-body {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.loading, .error, .no-folders {
  padding: 20px;
  text-align: center;
  color: #6b7280;
}

.error {
  color: #dc2626;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.folder-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.folder-item:hover {
  background: #f3f4f6;
}

.folder-item.parent {
  color: #6b7280;
  font-style: italic;
}

.picker-footer {
  padding: 12px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.btn-cancel {
  background: white;
  color: #374151;
}

.btn-cancel:hover {
  background: #f9fafb;
}

.btn-select {
  background: #1e40af;
  color: white;
  border-color: #1e40af;
}

.btn-select:hover {
  background: #1d4ed8;
}
</style>