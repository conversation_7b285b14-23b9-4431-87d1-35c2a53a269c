<template>
  <Modal v-model="show" title="Add Source">
    <div style="display:flex; flex-direction:column; gap:16px;">
      <div style="display:flex; flex-direction:column; gap:8px;">
        <label style="font-weight:600; color:#374151;">Source Type</label>
        <select v-model="kind" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;">
          <option value="fs">Filesystem</option>
          <option value="s3">S3</option>
        </select>
      </div>
      <div style="display:flex; flex-direction:column; gap:8px;">
        <label style="font-weight:600; color:#374151;">Name</label>
        <input v-model="name" placeholder="Enter source name" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;"/>
      </div>
      
      <!-- Filesystem Parameters -->
      <div v-if="kind==='fs'" style="display:flex; flex-direction:column; gap:8px;">
        <label style="font-weight:600; color:#374151;">Root Path</label>
        <div style="display:flex; gap:8px; align-items:center;">
          <input v-model="fs.root" placeholder="Enter filesystem root path" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px; flex:1;"/>
          <button v-if="showBrowseButton" @click="$emit('browse')" style="padding:8px 12px; border:1px solid #1e40af; border-radius:6px; background:#1e40af; color:white; cursor:pointer;">Browse</button>
        </div>
        <small v-if="errors.root" style="color:#dc2626;">{{ errors.root }}</small>
      </div>
      
      <!-- S3 Parameters -->
      <div v-else style="display:flex; flex-direction:column; gap:12px;">
        <label style="font-weight:600; color:#374151;">S3 Configuration</label>
        <div style="display:grid; grid-template-columns:repeat(3, minmax(200px, 1fr)); gap:8px;">
          <div style="display:flex; flex-direction:column; gap:4px;">
            <label style="font-size:14px; color:#4b5563;">Bucket *</label>
            <input v-model="s3.bucket" placeholder="my-bucket" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;"/>
          </div>
          <div style="display:flex; flex-direction:column; gap:4px;">
            <label style="font-size:14px; color:#4b5563;">Region *</label>
            <input v-model="s3.region" placeholder="us-east-1" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;"/>
          </div>
          <div style="display:flex; flex-direction:column; gap:4px;">
            <label style="font-size:14px; color:#4b5563;">Endpoint (optional)</label>
            <input v-model="s3.endpoint" placeholder="https://s3.amazonaws.com" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;"/>
          </div>
          <div style="display:flex; flex-direction:column; gap:4px;">
            <label style="font-size:14px; color:#4b5563;">Access Key ID</label>
            <input v-model="s3.accessKeyId" placeholder="AKIA..." style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;"/>
          </div>
          <div style="display:flex; flex-direction:column; gap:4px;">
            <label style="font-size:14px; color:#4b5563;">Secret Access Key</label>
            <input v-model="s3.secretAccessKey" placeholder="Enter secret key" type="password" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;"/>
          </div>
          <div style="display:flex; flex-direction:column; gap:4px;">
            <label style="font-size:14px; color:#4b5563;">Root Prefix (optional)</label>
            <input v-model="s3.rootPrefix" placeholder="folder/subfolder" style="padding:8px; border:1px solid #e5e7eb; border-radius:6px;"/>
          </div>
          <label style="display:flex; align-items:center; gap:6px; grid-column: 1 / -1; font-size:14px; color:#4b5563;">
            <input type="checkbox" v-model="s3.forcePathStyle"/> Force Path Style
          </label>
        </div>
        <small v-if="errors.bucket" style="color:#dc2626;">{{ errors.bucket }}</small>
        <small v-if="errors.region" style="color:#dc2626;">{{ errors.region }}</small>
      </div>
    </div>
    <template #footer>
      <div style="display:flex; gap:8px; justify-content:flex-end;">
        <button @click="cancel" style="padding:8px 16px; border:1px solid #e5e7eb; border-radius:6px; background:#fff; cursor:pointer;">Cancel</button>
        <button @click="submit" :disabled="!isValid" style="padding:8px 16px; border:1px solid #3b82f6; border-radius:6px; background:#3b82f6; color:#fff; cursor:pointer;" :style="{ opacity: isValid ? 1 : 0.6 }">Add Source</button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Modal from './Modal.vue'
import type { SourceKind } from '@/lib/sources'

interface Props {
  modelValue: boolean
  showBrowseButton?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: { kind: SourceKind, name: string, config: any }): void
  (e: 'browse'): void
}

const props = withDefaults(defineProps<Props>(), {
  showBrowseButton: false
})

const emit = defineEmits<Emits>()

const show = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const kind = ref<SourceKind>('fs')
const name = ref('')
const fs = ref<{root:string}>({ root: '' })
const s3 = ref<{bucket:string, region:string, accessKeyId:string, secretAccessKey:string, endpoint:string, forcePathStyle:boolean, rootPrefix:string}>({ 
  bucket:'', region:'', accessKeyId:'', secretAccessKey:'', endpoint:'', forcePathStyle:false, rootPrefix:'' 
})

const errors = computed(() => {
  const errs: Record<string,string> = {}
  if (!name.value.trim()) errs.name = 'Name required'
  if (kind.value==='fs') {
    if (!fs.value.root.trim()) errs.root = 'Root path required'
  } else {
    if (!s3.value.bucket.trim()) errs.bucket = 'Bucket required'
    if (!s3.value.region.trim()) errs.region = 'Region required'
  }
  return errs
})

const isValid = computed(() => Object.keys(errors.value).length === 0)

function reset() {
  kind.value = 'fs'
  name.value = ''
  fs.value = { root: '' }
  s3.value = { bucket:'', region:'', accessKeyId:'', secretAccessKey:'', endpoint:'', forcePathStyle:false, rootPrefix:'' }
}

function cancel() {
  reset()
  show.value = false
}

function submit() {
  if (!isValid.value) return
  
  const config = kind.value === 'fs' ? fs.value : s3.value
  emit('submit', { kind: kind.value, name: name.value, config })
  reset()
  show.value = false
}

// Reset form when modal is closed
watch(show, (newValue) => {
  if (!newValue) {
    reset()
  }
})
</script>