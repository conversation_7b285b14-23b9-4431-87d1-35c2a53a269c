<template>
  <Modal v-model="show" :title="modalTitle" :width="500">
    <div :style="MODAL_STYLES.container">
      <!-- User Info -->
      <div v-if="user" style="padding:16px; background:#f8fafc; border:1px solid #e2e8f0; border-radius:8px; margin-bottom:20px;">
        <div style="display:flex; align-items:center; gap:12px;">
          <div style="width:40px; height:40px; border-radius:50%; background:#3b82f6; display:flex; align-items:center; justify-content:center; color:#fff; font-weight:600; font-size:16px;">
            {{ (user.name || user.email).charAt(0).toUpperCase() }}
          </div>
          <div>
            <div style="font-weight:600; color:#1e293b; font-size:16px;">{{ user.name || user.email }}</div>
            <div style="font-size:14px; color:#64748b;">{{ user.email }}</div>
          </div>
        </div>
      </div>

      <!-- Role Assignment Section -->
      <div :style="MODAL_STYLES.fieldGroup">
        <div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:12px;">
          <label :style="MODAL_STYLES.label">Available Roles</label>
          <span style="font-size:12px; color:#64748b;">{{ assignedRoles.length }} assigned</span>
        </div>
        
        <div style="border:1px solid #e2e8f0; border-radius:8px; max-height:280px; overflow-y:auto;">
          <div v-for="role in availableRoles" :key="role.id" 
                style="padding:12px 16px; border-bottom:1px solid #f1f5f9; display:flex; align-items:center; gap:12px;">
            <input 
              type="checkbox" 
              :id="`role-${role.id}`"
              :checked="isRoleAssigned(role)"
              @change="toggleRole(role)"
              style="width:16px; height:16px; accent-color:#3b82f6;"
            />
            <label :for="`role-${role.id}`" style="cursor:pointer; flex:1;">
              <div style="font-weight:500; color:#1e293b; margin-bottom:2px;">{{ role.name }}</div>
              <div v-if="role.description" style="font-size:13px; color:#64748b; line-height:1.4;">{{ role.description }}</div>
            </label>
            <div v-if="isRoleAssigned(role)" style="width:20px; height:20px; border-radius:50%; background:#10b981; display:flex; align-items:center; justify-content:center;">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="3">
                <polyline points="20,6 9,17 4,12"></polyline>
              </svg>
            </div>
          </div>
          <div v-if="availableRoles.length === 0" style="text-align:center; padding:32px 16px; color:#64748b; font-style:italic;">
             No roles available
           </div>
         </div>
       </div>
    </div>
     
     <template #footer>
      <button 
        @click="$emit('update:modelValue', false)"
        :style="MODAL_STYLES.buttonCancel"
      >
        Cancel
      </button>
      <button 
        @click="handleSubmit"
        :style="MODAL_STYLES.buttonPrimary"
      >
        Update Roles
      </button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import Modal from './Modal.vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'
import { MODAL_STYLES } from '@/types/admin-modals'
import type { UserRoleModalProps, UserRoleModalEmits, UserRoleFormData, Role } from '@/types/admin-modals'

const props = defineProps<UserRoleModalProps>()
const emit = defineEmits<UserRoleModalEmits>()

const show = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const availableRoles = ref<Role[]>([])
const assignedRoles = ref<Role[]>([])

const modalTitle = computed(() => {
  return props.user ? `Manage Roles - ${props.user.name || props.user.email}` : 'Manage User Roles'
})

function isRoleAssigned(role: Role): boolean {
  return assignedRoles.value.some(r => r.id === role.id)
}

function toggleRole(role: Role) {
  const index = assignedRoles.value.findIndex(r => r.id === role.id)
  if (index >= 0) {
    assignedRoles.value.splice(index, 1)
  } else {
    assignedRoles.value.push(role)
  }
}

async function loadRoles() {
  try {
    const res = await authenticatedApiRequest('/v1/admin/roles')
    if (res.ok) {
      availableRoles.value = await res.json()
    } else {
      // Mock data for development
      availableRoles.value = [
        { id: 1, name: 'Editor', description: 'Can edit and manage content' },
        { id: 2, name: 'Viewer', description: 'Can view content only' },
        { id: 3, name: 'Moderator', description: 'Can moderate user content' }
      ]
    }
  } catch (e) {
    // Mock data for development
    availableRoles.value = [
      { id: 1, name: 'Editor', description: 'Can edit and manage content' },
      { id: 2, name: 'Viewer', description: 'Can view content only' },
      { id: 3, name: 'Moderator', description: 'Can moderate user content' }
    ]
  }
}

async function loadUserRoles() {
  if (!props.user) return
  
  try {
    const res = await authenticatedApiRequest(`/v1/admin/users/${props.user.sub}/roles`)
    if (res.ok) {
      const data = await res.json()
      const roleIds = data.roles || []
      
      // Fetch full role details for each role ID
      const roleDetails = []
      for (const roleId of roleIds) {
        const roleRes = await authenticatedApiRequest(`/v1/admin/roles/${roleId}`)
        if (roleRes.ok) {
          const roleDetail = await roleRes.json()
          roleDetails.push(roleDetail)
        }
      }
      assignedRoles.value = roleDetails
    } else {
      // Fallback to empty array since we need Role objects, not strings
      assignedRoles.value = []
    }
  } catch (e) {
    // Fallback to empty array since we need Role objects, not strings
    assignedRoles.value = []
  }
}

function handleSubmit() {
  const formData: UserRoleFormData = {
    roles: assignedRoles.value
  }
  emit('submit', formData)
  emit('update:modelValue', false)
}

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    loadRoles()
    loadUserRoles()
  }
})

watch(() => props.user, () => {
  if (props.modelValue && props.user) {
    loadUserRoles()
  }
})

onMounted(() => {
  if (props.modelValue) {
    loadRoles()
    loadUserRoles()
  }
})
</script>