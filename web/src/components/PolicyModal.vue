<template>
  <Modal :model-value="modelValue" :title="mode === 'add' ? 'Add Policy' : 'Edit Policy'" @update:modelValue="$emit('update:modelValue', $event)" :width="700">
    <div style="margin-top: -2px; padding: 22px; border: 2px solid #cbd5e1; border-radius: 8px; background: #fafafa;">
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 22px;">
        <div>
          <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Subject Type</label>
          <select v-model="formData.subject_type" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white;">
            <option value="all">All Users</option>
            <option value="user">Specific User</option>
            <option value="group">Specific Group</option>
          </select>
        </div>
        
        <div v-if="formData.subject_type === 'user'">
          <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Select User</label>
          <select v-model="formData.subject_id" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white;">
            <option value="">Choose a user...</option>
            <option v-for="user in users" :key="user.id" :value="user.id">{{ user.email }}</option>
          </select>
        </div>
        
        <div v-if="formData.subject_type === 'group'">
          <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Select Group</label>
          <select v-model="formData.subject_id" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white;">
            <option value="">Choose a group...</option>
            <option v-for="group in groups" :key="group.id" :value="group.id">{{ group.name }}</option>
          </select>
        </div>
        
        <div v-if="formData.subject_type === 'all'">
          <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Source</label>
          <select v-model="formData.source_id" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white;">
            <option value="">All Sources</option>
            <option v-for="s in sources" :key="s.id" :value="s.id">{{ s.name }}</option>
          </select>
        </div>
        
        <div v-else>
          <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Source</label>
          <select v-model="formData.source_id" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white;">
            <option value="">All Sources</option>
            <option v-for="s in sources" :key="s.id" :value="s.id">{{ s.name }}</option>
          </select>
        </div>
      </div>
      
      <div style="margin-bottom: 20px; grid-column: 1 / -1;">
        <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Path Prefix</label>
        <input v-model="formData.path_prefix" placeholder="Path prefix (e.g. / or /docs)" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white; box-sizing: border-box;"/>
      </div>
      
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
        <div>
          <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Action</label>
          <select v-model="formData.action" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white;">
            <optgroup v-for="category in actionCategories" :key="category" :label="category">
              <option v-for="action in actionsByCategory[category]" :key="action.id" :value="action.name">
                {{ action.name }} - {{ action.description }}
              </option>
            </optgroup>
          </select>
        </div>
        
        <div>
          <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Effect</label>
          <select v-model="formData.effect" style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; width: 100%; background: white;">
            <option value="allow">Allow</option>
            <option value="deny">Deny</option>
          </select>
        </div>
      </div>
      
      <div style="margin-bottom: 20px;">
        <label style="font-weight: 600; color: #374151; margin-bottom: 8px; display: block;">Expiry Date (Local Time)</label>
        <div style="display: flex; align-items: center; gap: 12px;">
          <input 
            type="datetime-local" 
            v-model="formData.expires_at" 
            :disabled="infiniteExpiry" 
            style="padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; flex: 1; background: white"
          />
          <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; color: #374151; white-space: nowrap; background: white; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
            <input 
              type="checkbox" 
              v-model="infiniteExpiry" 
              @change="handleInfiniteExpiryChange"
              style="margin: 0"
            />
            Never Expires
          </label>
        </div>
        <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">Times are in your local timezone and will be converted to UTC for storage</div>
      </div>
    </div>
    
    <template #footer>
      <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 0; margin-top: -2px; margin-bottom: -2px;">
        <button @click="$emit('update:modelValue', false)" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; cursor: pointer">
          Cancel
        </button>
        <button @click="handleSubmit" style="padding: 8px 16px; border: none; border-radius: 6px; background: #3b82f6; color: white; cursor: pointer">
          {{ mode === 'add' ? 'Add Policy' : 'Update Policy' }}
        </button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import Modal from './Modal.vue'
import { authenticatedApiRequest } from '@/lib/api'

interface User {
  id: string
  email: string
}

interface Group {
  id: string
  name: string
}

interface Source {
  id: string
  name: string
}

interface Action {
  id: number
  name: string
  description: string
  category: string
}

interface Policy {
  id?: string
  subject_type: string
  subject_id?: string
  source_id?: string
  path_prefix: string
  action: string
  effect: string
  expires_at?: string | null
}

interface Props {
  modelValue: boolean
  mode?: 'add' | 'edit'
  policy?: Policy | null
  users?: User[]
  groups?: Group[]
  sources?: Source[]
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add',
  policy: null,
  users: () => [],
  groups: () => [],
  sources: () => []
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: any]
}>()

const formData = ref({
  subject_type: 'all',
  subject_id: '',
  source_id: '',
  path_prefix: '/',
  action: 'view',
  effect: 'allow',
  expires_at: '' as string | null
})

const infiniteExpiry = ref(false)
const actions = ref<Action[]>([])

// Computed properties for action categorization
const actionsByCategory = computed(() => {
  const grouped: Record<string, Action[]> = {}
  actions.value.forEach(action => {
    if (!grouped[action.category]) {
      grouped[action.category] = []
    }
    grouped[action.category].push(action)
  })
  return grouped
})

const actionCategories = computed(() => {
  return Object.keys(actionsByCategory.value).sort()
})

// Fetch actions from API
const fetchActions = async () => {
  try {
    const response = await authenticatedApiRequest('/v1/admin/permissions')
    if (response.ok) {
      actions.value = await response.json()
    }
  } catch (error) {
    console.error('Failed to fetch actions:', error)
    // Fallback to legacy actions if API fails
    actions.value = [
      { id: 1, name: 'view', description: 'View files and directories', category: 'Read' },
      { id: 2, name: 'download', description: 'Download files', category: 'Read' },
      { id: 3, name: 'upload', description: 'Upload files', category: 'Write' }
    ]
  }
}

onMounted(() => {
  fetchActions()
})

// Watch for policy changes in edit mode
watch(() => props.policy, (policy) => {
  if (policy && props.mode === 'edit') {
    formData.value = {
      subject_type: policy.subject_type,
      subject_id: policy.subject_id || '',
      source_id: policy.source_id || '',
      path_prefix: policy.path_prefix,
      action: policy.action,
      effect: policy.effect,
      expires_at: policy.expires_at ? new Date(policy.expires_at).toISOString().slice(0, 16) : ''
    }
    infiniteExpiry.value = !policy.expires_at
  }
}, { immediate: true })

// Reset form when switching to add mode
watch(() => props.mode, (mode) => {
  if (mode === 'add') {
    formData.value = {
      subject_type: 'all',
      subject_id: '',
      source_id: '',
      path_prefix: '/',
      action: 'view',
      effect: 'allow',
      expires_at: ''
    }
    infiniteExpiry.value = false
  }
})

const handleInfiniteExpiryChange = () => {
  if (infiniteExpiry.value) {
    formData.value.expires_at = ''
  }
}

const handleSubmit = () => {
  const submitData = { ...formData.value }
  
  // Handle expiry date conversion
  if (infiniteExpiry.value) {
    submitData.expires_at = null
  } else if (submitData.expires_at) {
    submitData.expires_at = new Date(submitData.expires_at as string).toISOString()
  }
  
  emit('submit', submitData)
}
</script>