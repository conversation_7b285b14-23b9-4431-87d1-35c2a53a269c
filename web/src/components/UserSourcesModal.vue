<template>
  <div v-if="modelValue" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>Source Access - {{ user?.name || user?.email }}</h3>
        <button @click="closeModal" class="close-button">
          <Icon name="x" :size="20" />
        </button>
      </div>
      
      <div class="modal-body">
        <div v-if="user?.sources && user.sources.length > 0" class="sources-list">
          <div v-for="source in user.sources" :key="source.id" class="source-item">
            <div class="source-info">
              <div class="source-name">{{ source.name }}</div>
              <div class="source-type" :style="getSourceChipStyle(source.kind)">
                {{ source.kind.toUpperCase() }}
              </div>
            </div>
            <div class="source-permissions">
              <div v-if="source.userRole" class="user-role">
                <span class="role-label">Role:</span>
                <span class="role-chip" :class="`role-${source.userRole}`">
                  {{ source.userRole.toUpperCase() }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="no-sources">
          <Icon name="folder-x" :size="48" color="#9ca3af" />
          <p>No sources available or user has no role assigned.</p>
        </div>
      </div>
      
      <div class="modal-footer">
        <button @click="closeModal" class="btn-secondary">
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import Icon from './Icon.vue'

interface Permission {
  action: string
  allowed: boolean
  source: 'explicit' | 'role' | 'denied'
  effect?: 'allow' | 'deny'
}

interface User {
  sub: string
  name?: string
  email: string
  sources?: Array<{
    id: number
    name: string
    kind: string
    userRole?: string
    permissions?: Permission[]
  }>
  roles?: Array<{
    id: string
    name: string
    description?: string
  }>
}

interface Props {
  modelValue: boolean
  user?: User | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

function closeModal() {
  emit('update:modelValue', false)
}

function getSourceChipStyle(kind: string) {
  const styles: Record<string, string> = {
    'local': 'background:#dbeafe; color:#1d4ed8; border:1px solid #3b82f6;',
    'gdrive': 'background:#dcfce7; color:#166534; border:1px solid #22c55e;',
    's3': 'background:#fef3c7; color:#92400e; border:1px solid #f59e0b;',
    'azure': 'background:#e0e7ff; color:#3730a3; border:1px solid #6366f1;'
  }
  return styles[kind] || 'background:#f3f4f6; color:#374151; border:1px solid #9ca3af;'
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.source-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: #f9fafb;
}

.source-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.source-name {
  font-weight: 600;
  color: #111827;
  font-size: 16px;
}

.source-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.source-permissions {
  margin-top: 12px;
}

.user-role {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.role-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.role-chip {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.role-admin {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #f87171;
}

.role-editor {
  background: #f0f9ff;
  color: #0284c7;
  border: 1px solid #38bdf8;
}

.role-viewer {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #4ade80;
}



.no-sources {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-sources p {
  margin: 16px 0 0 0;
  color: #6b7280;
  font-size: 16px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

.btn-secondary {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  font-weight: 500;
}

.btn-secondary:hover {
  background: #f9fafb;
}
</style>