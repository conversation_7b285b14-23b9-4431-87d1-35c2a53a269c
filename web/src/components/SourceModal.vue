<template>
  <Modal v-model="show" :title="modalTitle" :width="700">
    <div :style="MODAL_STYLES.container">
      <!-- Source Type -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Source Type</label>
        <select 
          v-model="formData.kind" 
          :style="MODAL_STYLES.select"
          :disabled="mode === 'edit'"
        >
          <option value="" disabled>Select...</option>
          <option value="fs">Filesystem</option>
          <option value="s3">S3</option>
        </select>
      </div>

      <!-- Source Name -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Name *</label>
        <input 
          v-model="formData.name" 
          placeholder="Enter source name" 
          :style="`${MODAL_STYLES.input} border-color: ${errors.name ? '#dc2626' : '#e5e7eb'};`"
          @blur="validateForm"
        />
        <small v-if="errors.name" :style="MODAL_STYLES.error">{{ errors.name }}</small>
      </div>
      
      <!-- Configuration Fields (shown only when source type is selected) -->
      <div v-if="formData.kind">
        <!-- Filesystem Configuration -->
        <div v-if="formData.kind === 'fs'" :style="MODAL_STYLES.fieldGroup">
          <label :style="MODAL_STYLES.label">Root Path *</label>
          <div style="display:flex; gap:8px; align-items:center;">
            <input 
              v-model="fsConfig.root" 
              placeholder="Enter filesystem root path" 
              :style="`${MODAL_STYLES.input} flex:1; border-color: ${errors.root ? '#dc2626' : '#e5e7eb'};`"
              @blur="validateForm"
            />
            <button 
              v-if="showBrowseButton" 
              @click="$emit('browse')" 
              style="padding:8px 12px; border:1px solid #1e40af; border-radius:6px; background:#1e40af; color:white; cursor:pointer; font-size:14px;"
            >
              Browse
            </button>
          </div>
          <small v-if="errors.root" :style="MODAL_STYLES.error">{{ errors.root }}</small>
        </div>
        
        <!-- S3 Configuration -->
        <div v-if="formData.kind === 's3'" :style="MODAL_STYLES.fieldGroup">
          <label :style="MODAL_STYLES.label">S3 Configuration</label>
          <div :style="MODAL_STYLES.grid">
            <div :style="MODAL_STYLES.fieldGroup">
              <label style="font-size:13px; color:#6b7280;">Bucket *</label>
              <input 
                v-model="s3Config.bucket" 
                placeholder="my-bucket" 
                :style="`${MODAL_STYLES.input} border-color: ${errors.bucket ? '#dc2626' : '#e5e7eb'};`"
                @blur="validateForm"
              />
            </div>
            <div :style="MODAL_STYLES.fieldGroup">
              <label style="font-size:13px; color:#6b7280;">Region *</label>
              <input 
                v-model="s3Config.region" 
                placeholder="us-east-1" 
                :style="`${MODAL_STYLES.input} border-color: ${errors.region ? '#dc2626' : '#e5e7eb'};`"
                @blur="validateForm"
              />
            </div>
            <div :style="`${MODAL_STYLES.fieldGroup} ${MODAL_STYLES.gridFull}`">
              <label style="font-size:13px; color:#6b7280;">Endpoint (optional)</label>
              <input 
                v-model="s3Config.endpoint" 
                placeholder="https://s3.amazonaws.com" 
                :style="MODAL_STYLES.input"
              />
            </div>
            <div :style="MODAL_STYLES.fieldGroup">
              <label style="font-size:13px; color:#6b7280;">Access Key ID</label>
              <input 
                v-model="s3Config.accessKeyId" 
                placeholder="AKIA..." 
                :style="MODAL_STYLES.input"
              />
            </div>
            <div :style="MODAL_STYLES.fieldGroup">
              <label style="font-size:13px; color:#6b7280;">Secret Access Key</label>
              <input 
                v-model="s3Config.secretAccessKey" 
                placeholder="Enter secret key" 
                type="password" 
                :style="MODAL_STYLES.input"
              />
            </div>
            <div :style="`${MODAL_STYLES.fieldGroup} ${MODAL_STYLES.gridFull}`">
              <label style="font-size:13px; color:#6b7280;">Root Prefix (optional)</label>
              <input 
                v-model="s3Config.rootPrefix" 
                placeholder="folder/subfolder" 
                :style="MODAL_STYLES.input"
              />
            </div>
            <label :style="`${MODAL_STYLES.checkbox} ${MODAL_STYLES.gridFull}`">
              <input type="checkbox" v-model="s3Config.forcePathStyle"/> 
              Force Path Style
            </label>
          </div>
          <small v-if="errors.bucket" :style="MODAL_STYLES.error">{{ errors.bucket }}</small>
          <small v-if="errors.region" :style="MODAL_STYLES.error">{{ errors.region }}</small>
        </div>
      </div>
    </div>

    <template #footer>
      <div :style="MODAL_STYLES.footer">
        <button @click="cancel" :style="MODAL_STYLES.buttonCancel" :disabled="isSubmitting">
          Cancel
        </button>
        <button 
          @click="submit" 
          :disabled="!isValid || isSubmitting" 
          :style="`${MODAL_STYLES.buttonPrimary} opacity:${(isValid && !isSubmitting) ? 1 : 0.6}; cursor: ${(isValid && !isSubmitting) ? 'pointer' : 'not-allowed'};`"
        >
          {{ isSubmitting ? 'Saving...' : (mode === 'add' ? 'Add Source' : 'Update Source') }}
        </button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { nextTick, ref, computed, watch } from 'vue'
import Modal from './Modal.vue'
import type { 
  SourceModalProps, 
  SourceModalEmits, 
  SourceFormData, 
  ValidationErrors,
  FilesystemConfig,
  S3Config
} from '@/types/admin-modals'
import { MODAL_STYLES } from '@/types/admin-modals'
import { useBaseModal } from '@/composables/useBaseModal'

const props = withDefaults(defineProps<SourceModalProps>(), {
  mode: 'add',
  showBrowseButton: false
})

const emit = defineEmits<SourceModalEmits>()

const modalTitle = computed(() => 
  props.mode === 'add' ? 'Add Source' : 'Edit Source'
)

const fsConfig = ref<FilesystemConfig>({ root: '' })
const s3Config = ref<S3Config>({
  bucket: '',
  region: '',
  endpoint: '',
  accessKeyId: '',
  secretAccessKey: '',
  rootPrefix: '',
  forcePathStyle: false
})

// Validation function
const validateSourceData = (data: SourceFormData): ValidationErrors => {
  const errs: ValidationErrors = {}
  
  if (!data.name?.trim()) {
    errs.name = 'Source name is required'
  } else if (data.name.trim().length < 2) {
    errs.name = 'Source name must be at least 2 characters long'
  }
  
  if (data.kind === 'fs') {
    const config = data.config as FilesystemConfig
    if (!config?.root?.trim()) {
      errs.root = 'Root path is required for filesystem sources'
    }
  } else if (data.kind === 's3') {
    const config = data.config as S3Config
    if (!config?.bucket?.trim()) {
      errs.bucket = 'Bucket name is required for S3 sources'
    }
    if (!config?.region?.trim()) {
      errs.region = 'Region is required for S3 sources'
    }
  }
  
  return errs
}

// Load source data function
const loadSourceData = () => {
  if (props.mode === 'edit' && props.source) {
    formData.value.kind = props.source.kind
    formData.value.name = props.source.name
    
    const config = typeof props.source.config === 'string' 
      ? JSON.parse(props.source.config) 
      : props.source.config
    
    if (props.source.kind === 'fs') {
      fsConfig.value = { ...config }
      formData.value.config = fsConfig.value
    } else {
      s3Config.value = { 
        bucket: config.bucket || '',
        region: config.region || '',
        endpoint: config.endpoint || '',
        accessKeyId: config.accessKeyId || '',
        secretAccessKey: config.secretAccessKey || '',
        rootPrefix: config.rootPrefix || '',
        forcePathStyle: config.forcePathStyle || false
      }
      formData.value.config = s3Config.value
    }
  }
}

// Reset configs function
const resetConfigs = () => {
  fsConfig.value = { root: '' }
  s3Config.value = {
    bucket: '',
    region: '',
    endpoint: '',
    accessKeyId: '',
    secretAccessKey: '',
    rootPrefix: '',
    forcePathStyle: false
  }
  formData.value.config = { root: '' }
}

// Use base modal composable
const {
  show,
  formData,
  errors,
  isSubmitting,
  isValid,
  validateForm,
  cancel,
  submit: baseSubmit
} = useBaseModal<SourceFormData>(props, emit, {
  initialData: () => ({
    kind: '',
    name: '',
    config: { root: '' } as FilesystemConfig | S3Config
  }),
  validate: validateSourceData,
  onLoadData: loadSourceData,
  onReset: resetConfigs
})

// Custom submit function to handle config transformation
const submit = async () => {
  if (!isValid.value || isSubmitting.value) return
  
  try {
    isSubmitting.value = true
    const config = formData.value.kind === 'fs' ? fsConfig.value : s3Config.value
    
    emit('submit', {
      kind: formData.value.kind,
      name: formData.value.name,
      config
    })
    
    show.value = false
  } catch (error) {
    console.error('Error submitting form:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Update formData.config when configs change
watch([fsConfig, s3Config, () => formData.value.kind], () => {
  if (formData.value.kind === 'fs') {
    formData.value.config = fsConfig.value
  } else if (formData.value.kind === 's3') {
    formData.value.config = s3Config.value
  }
  // Trigger validation when config changes
  validateForm()
}, { deep: true })

// Watch for kind changes to reset config
watch(() => formData.value.kind, () => {
  if (formData.value.kind === 'fs') {
    fsConfig.value = { root: '' }
    formData.value.config = fsConfig.value
  } else if (formData.value.kind === 's3') {
    s3Config.value = {
      bucket: '',
      region: '',
      endpoint: '',
      accessKeyId: '',
      secretAccessKey: '',
      rootPrefix: '',
      forcePathStyle: false
    }
    formData.value.config = s3Config.value
  }
})

// Watch for prop changes - use nextTick to ensure proper timing
watch([() => props.mode, () => props.source], async () => {
  if (show.value && props.mode === 'edit' && props.source) {
    await nextTick()
    loadSourceData()
  }
}, { immediate: true })

// Also watch for show changes to load data when modal opens
watch(show, async (newValue) => {
  if (newValue && props.mode === 'edit' && props.source) {
    await nextTick()
    loadSourceData()
  }
})
</script>