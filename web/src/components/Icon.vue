<template>
  <svg :width="size" :height="size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" :aria-label="name">
    <template v-if="name==='home'">
      <path d="M3 11L12 4l9 7" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M5 10v9h14v-9" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='computer'">
      <rect x="3" y="5" width="18" height="12" rx="2" :stroke="color" :stroke-width="stroke"/>
      <path d="M8 21h8" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M10 17v4M14 17v4" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <template v-else-if="name==='cloud'">
      <path d="M7 18h10a4 4 0 0 0 0-8 6 6 0 0 0-11-1 4 4 0 0 0 1 9z" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='up'">
      <path d="M12 19V5" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M6 11l6-6 6 6" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='refresh'">
      <path d="M20 11a8 8 0 1 1-2.3-5.7" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M20 4v6h-6" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='folder'">
      <path d="M3 7h7l2 2h9v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7z" :stroke="color" :stroke-width="stroke" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='upload'">
      <path d="M12 15V5" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M7 10l5-5 5 5" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M5 19h14" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <template v-else-if="name==='download'">
      <path d="M12 5v10" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M7 10l5 5 5-5" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M5 19h14" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <template v-else-if="name==='edit'">
      <path d="M4 20l4-1 9-9a2 2 0 1 0-3-3L5 16l-1 4z" :stroke="color" :stroke-width="stroke" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='move'">
      <path d="M7 12H3l3-3M3 12l3 3" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M17 12h4l-3 3M21 12l-3-3" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='trash'">
      <path d="M4 7h16" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M6 7l1 13h10l1-13" :stroke="color" :stroke-width="stroke" stroke-linejoin="round"/>
      <path d="M9 7V4h6v3" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='eye'">
      <path d="M2 12s4-7 10-7 10 7 10 7-4 7-10 7S2 12 2 12z" :stroke="color" :stroke-width="stroke" stroke-linejoin="round"/>
      <circle cx="12" cy="12" r="3" :stroke="color" :stroke-width="stroke"/>
    </template>
    <template v-else-if="name==='plus'">
      <path d="M12 5v14" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M5 12h14" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <template v-else-if="name==='edit'">
      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="m18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='trash'">
      <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14ZM10 11v6M14 11v6" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='test'">
      <path d="M9 12l2 2 4-4" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="12" r="10" :stroke="color" :stroke-width="stroke"/>
    </template>
    <template v-else-if="name==='search'">
      <circle cx="11" cy="11" r="6" :stroke="color" :stroke-width="stroke"/>
      <path d="M16 16l5 5" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <template v-else-if="name==='x'">
      <path d="M6 6l12 12M18 6L6 18" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <template v-else-if="name==='plus'">
      <path d="M12 5v14" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M5 12h14" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <template v-else-if="name==='check'">
      <path d="M5 13l4 4L19 7" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <!-- Sort (toggle) -->
    <template v-else-if="name==='sort'">
      <path d="M8 6h8M8 12h6M8 18h4" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
      <path d="M6 5l-2 2 2 2M18 17l2-2-2-2" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <!-- Check Square (Select All) -->
    <template v-else-if="name==='check-square'">
      <rect x="3" y="3" width="18" height="18" rx="2" :stroke="color" :stroke-width="stroke"/>
      <path d="M8 12l3 3 5-6" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <!-- X Square (Clear All) -->
    <template v-else-if="name==='x-square'">
      <rect x="3" y="3" width="18" height="18" rx="2" :stroke="color" :stroke-width="stroke"/>
      <path d="M9 9l6 6M15 9l-6 6" :stroke="color" :stroke-width="stroke" stroke-linecap="round"/>
    </template>
    <!-- Crown (Admin) -->
    <template v-else-if="name==='crown'">
      <path d="M2 18h20l-2-6-4 2-4-4-4 4-4-2z" :stroke="color" :stroke-width="stroke" stroke-linejoin="round"/>
      <circle cx="7" cy="8" r="1" :fill="color"/>
      <circle cx="12" cy="6" r="1" :fill="color"/>
      <circle cx="17" cy="8" r="1" :fill="color"/>
    </template>
    <!-- Shield (Make Admin) -->
    <template v-else-if="name==='shield'">
      <path d="M12 2l8 3v7c0 5-8 10-8 10s-8-5-8-10V5l8-3z" :stroke="color" :stroke-width="stroke" stroke-linejoin="round"/>
    </template>
    <!-- Shield Check (Permissions) -->
    <template v-else-if="name==='shield-check'">
      <path d="M12 2l8 3v7c0 5-8 10-8 10s-8-5-8-10V5l8-3z" :stroke="color" :stroke-width="stroke" stroke-linejoin="round"/>
      <path d="M9 12l2 2 4-4" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='settings'">
      <circle cx="12" cy="12" r="3" :stroke="color" :stroke-width="stroke"/>
      <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" :stroke="color" :stroke-width="stroke"/>
    </template>
    <template v-else-if="name==='users'">
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="9" cy="7" r="4" :stroke="color" :stroke-width="stroke"/>
      <path d="M23 21v-2a4 4 0 0 0-3-3.87" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M16 3.13a4 4 0 0 1 0 7.75" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='user-group'">
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="9" cy="7" r="4" :stroke="color" :stroke-width="stroke"/>
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M16 3.13a4 4 0 0 1 0 7.75" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='lock'">
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2" :stroke="color" :stroke-width="stroke"/>
      <path d="M7 11V7a5 5 0 0 1 10 0v4" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='admin'">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='group'">
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="9" cy="7" r="4" :stroke="color" :stroke-width="stroke"/>
      <path d="M23 21v-2a4 4 0 0 0-3-3.87" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M16 3.13a4 4 0 0 1 0 7.75" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='zap'">
      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" :stroke="color" :stroke-width="stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </template>
    <template v-else-if="name==='pause'">
      <rect x="6" y="4" width="4" height="16" :fill="color"/>
      <rect x="14" y="4" width="4" height="16" :fill="color"/>
    </template>
  </svg>
</template>

<script setup lang="ts">
withDefaults(defineProps<{ name: string; size?: number; color?: string; stroke?: number }>(), {
  size: 16,
  color: '#0b1022',
  stroke: 2.25,
})
</script>
