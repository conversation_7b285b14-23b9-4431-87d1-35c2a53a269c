<template>
  <Modal v-model="show" :title="modalTitle" :width="500">
    <div :style="MODAL_STYLES.container">
      <!-- Email Field -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Email Address *</label>
        <input 
          v-model="formData.email" 
          type="email"
          placeholder="Enter email address" 
          :style="MODAL_STYLES.input + (errors.email ? ' border-color:#dc2626;' : ' border-color:#e5e7eb;')"
          :disabled="mode === 'edit'"
          @blur="validateForm"
        />
        <small v-if="errors.email" :style="MODAL_STYLES.error">{{ errors.email }}</small>
      </div>

      <!-- Name Field -->
      <div :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Display Name</label>
        <input 
          v-model="formData.name" 
          placeholder="Enter display name (optional)" 
          :style="MODAL_STYLES.input + (errors.name ? ' border-color:#dc2626;' : ' border-color:#e5e7eb;')"
          @blur="validateForm"
        />
        <small v-if="errors.name" :style="MODAL_STYLES.error">{{ errors.name }}</small>
      </div>



      <!-- Edit Mode: Additional Info -->
      <div v-if="mode === 'edit' && user" :style="MODAL_STYLES.fieldGroup">
        <label :style="MODAL_STYLES.label">Account Status</label>
        <div style="display:flex; gap:8px; align-items:center;">
          <span 
            :style="`padding:4px 8px; border-radius:12px; font-size:12px; font-weight:500; ${
              user.blocked 
                ? 'background:#fef2f2; color:#7f1d1d; border:1px solid #ef4444;' 
                : 'background:#ecfdf5; color:#065f46; border:1px solid #10b981;'
            }`"
          >
            {{ user.blocked ? 'Blocked' : 'Active' }}
          </span>
          <span 
            v-if="user.roles?.includes('admin') || user.is_admin"
            style="padding:4px 8px; border-radius:12px; font-size:12px; font-weight:500; background:#eff6ff; color:#1e40af; border:1px solid #3b82f6;"
          >
            Administrator
          </span>
        </div>
      </div>
    </div>

    <template #footer>
      <div :style="MODAL_STYLES.footer">
        <button 
          @click="cancel" 
          :style="MODAL_STYLES.buttonCancel"
          :disabled="isSubmitting"
        >
          Cancel
        </button>
        <button 
          @click="submit" 
          :disabled="!isValid || isSubmitting" 
          :style="`${MODAL_STYLES.buttonPrimary} opacity:${(isValid && !isSubmitting) ? 1 : 0.6};`"
        >
          {{ isSubmitting ? 'Saving...' : (mode === 'add' ? 'Add User' : 'Update User') }}
        </button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import Modal from './Modal.vue'
import type { UserModalProps, UserModalEmits, UserFormData, ValidationErrors } from '@/types/admin-modals'
import { MODAL_STYLES } from '@/types/admin-modals'
import { useBaseModal } from '@/composables/useBaseModal'

const props = withDefaults(defineProps<UserModalProps>(), {
  mode: 'add'
})

const emit = defineEmits<UserModalEmits>()

const modalTitle = computed(() => 
  props.mode === 'add' ? 'Create User' : 'Edit User'
)

// Validation function
const validateUserData = (data: UserFormData): Record<string, string> => {
  const errs: Record<string, string> = {}
  
  if (!data.email?.trim()) {
    errs.email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errs.email = 'Please enter a valid email address'
  }
  
  if (data.name && data.name.trim().length > 0 && data.name.trim().length < 2) {
    errs.name = 'Name must be at least 2 characters long'
  }
  
  return errs
}

// Load user data function
const loadUserData = () => {
  if (props.mode === 'edit' && props.user) {
    formData.value = {
      email: props.user.email,
      name: props.user.name || '',
      roles: props.user.roles || (props.user.is_admin ? ['admin'] : [])
    }
  }
}

// Use base modal composable
const {
  show,
  formData,
  errors,
  isSubmitting,
  isValid,
  validateForm,
  cancel,
  submit
} = useBaseModal<UserFormData>(props, emit, {
  initialData: () => ({
    email: '',
    name: '',
    roles: []
  }),
  validate: validateUserData,
  onLoadData: loadUserData
})



// Watch for prop changes to reload data
watch([() => props.mode, () => props.user], loadUserData, { immediate: true })
</script>