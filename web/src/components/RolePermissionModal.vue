<template>
  <div v-if="show" style="position:fixed; inset:0; background:rgba(0,0,0,0.5); display:flex; align-items:center; justify-content:center; z-index:1000;">
    <div style="background:white; padding:32px; border-radius:12px; width:900px; max-width:95vw; max-height:85vh; overflow-y:auto; box-shadow:0 25px 50px -12px rgba(0,0,0,0.25);">
      <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:24px;">
        <h3 style="margin:0; font-size:20px; font-weight:600; color:#111827;">Manage Permissions - {{ role?.name }}</h3>
        <button @click="$emit('close')" style="padding:8px; border:none; background:#f3f4f6; border-radius:6px; cursor:pointer; font-size:18px; color:#6b7280; transition:all 0.2s;" @mouseover="($event.target as HTMLElement).style.background='#e5e7eb'" @mouseout="($event.target as HTMLElement).style.background='#f3f4f6'">&times;</button>
      </div>
      
      <div v-if="role?.is_builtin" style="padding:16px; background:#fef3c7; border:1px solid #f59e0b; border-radius:8px; margin-bottom:20px; color:#92400e;">
        <div style="display:flex; align-items:center; gap:8px;">
          <svg style="width:20px; height:20px; fill:currentColor;" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <strong>Note:</strong> This is a built-in role. Permissions cannot be modified.
        </div>
      </div>
      
      <div v-if="loading" style="text-align:center; padding:60px; color:#6b7280;">
        <div style="display:inline-block; width:32px; height:32px; border:3px solid #e5e7eb; border-top:3px solid #3b82f6; border-radius:50%; animation:spin 1s linear infinite;"></div>
        <div style="margin-top:16px; font-size:16px;">Loading permissions...</div>
      </div>
      
      <div v-else>
        <div style="margin-bottom:20px;">
          <h4 style="margin:0 0 16px 0; font-size:16px; font-weight:600; color:#374151;">Permission Categories</h4>
          <div style="display:grid; grid-template-columns:1fr 1fr 1fr; gap:24px;">
            <!-- Read Permissions -->
            <div style="border:1px solid #e5e7eb; border-radius:8px; padding:16px; background:#fafafa;">
              <h5 style="margin:0 0 12px 0; font-size:14px; font-weight:600; color:#059669; display:flex; align-items:center; gap:8px;">
                <svg style="width:16px; height:16px; fill:currentColor;" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                </svg>
                Read Permissions
              </h5>
              <div v-for="permission in getPermissionsByCategory('read')" :key="permission.id" style="display:flex; align-items:flex-start; padding:8px 0; border-bottom:1px solid #f0f0f0;">
                <input 
                  type="checkbox" 
                  :id="`perm-${permission.id}`"
                  :checked="selectedPermissions.includes(permission.id)"
                  @change="togglePermission(permission.id)"
                  :disabled="!!role?.is_builtin"
                  style="margin-right:10px; margin-top:2px;"
                />
                <label :for="`perm-${permission.id}`" style="flex:1; cursor:pointer; display:flex; flex-direction:column;">
                  <span style="font-weight:500; color:#111827; font-size:13px;">{{ permission.name }}</span>
                  <span style="font-size:11px; color:#6b7280; line-height:1.3;">{{ permission.description }}</span>
                </label>
              </div>
            </div>
            
            <!-- Write Permissions -->
            <div style="border:1px solid #e5e7eb; border-radius:8px; padding:16px; background:#fafafa;">
              <h5 style="margin:0 0 12px 0; font-size:14px; font-weight:600; color:#dc2626; display:flex; align-items:center; gap:8px;">
                <svg style="width:16px; height:16px; fill:currentColor;" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
                Write Permissions
              </h5>
              <div v-for="permission in getPermissionsByCategory('write')" :key="permission.id" style="display:flex; align-items:flex-start; padding:8px 0; border-bottom:1px solid #f0f0f0;">
                <input 
                  type="checkbox" 
                  :id="`perm-${permission.id}`"
                  :checked="selectedPermissions.includes(permission.id)"
                  @change="togglePermission(permission.id)"
                  :disabled="!!role?.is_builtin"
                  style="margin-right:10px; margin-top:2px;"
                />
                <label :for="`perm-${permission.id}`" style="flex:1; cursor:pointer; display:flex; flex-direction:column;">
                  <span style="font-weight:500; color:#111827; font-size:13px;">{{ permission.name }}</span>
                  <span style="font-size:11px; color:#6b7280; line-height:1.3;">{{ permission.description }}</span>
                </label>
              </div>
            </div>
            
            <!-- Admin & Utility Permissions -->
            <div style="border:1px solid #e5e7eb; border-radius:8px; padding:16px; background:#fafafa;">
              <h5 style="margin:0 0 12px 0; font-size:14px; font-weight:600; color:#7c3aed; display:flex; align-items:center; gap:8px;">
                <svg style="width:16px; height:16px; fill:currentColor;" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                </svg>
                Admin & Utility
              </h5>
              <div v-for="permission in getPermissionsByCategory(['admin', 'utility'])" :key="permission.id" style="display:flex; align-items:flex-start; padding:8px 0; border-bottom:1px solid #f0f0f0;">
                <input 
                  type="checkbox" 
                  :id="`perm-${permission.id}`"
                  :checked="selectedPermissions.includes(permission.id)"
                  @change="togglePermission(permission.id)"
                  :disabled="!!role?.is_builtin"
                  style="margin-right:10px; margin-top:2px;"
                />
                <label :for="`perm-${permission.id}`" style="flex:1; cursor:pointer; display:flex; flex-direction:column;">
                  <span style="font-weight:500; color:#111827; font-size:13px;">{{ permission.name }}</span>
                  <span style="font-size:11px; color:#6b7280; line-height:1.3;">{{ permission.description }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        
        <div style="display:flex; gap:12px; justify-content:flex-end; margin-top:24px; padding-top:20px; border-top:1px solid #e5e7eb;">
          <button @click="$emit('close')" style="padding:10px 20px; border:1px solid #d1d5db; background:white; border-radius:6px; cursor:pointer; color:#374151; font-weight:500; transition:all 0.2s;" @mouseover="($event.target as HTMLElement).style.background='#f9fafb'" @mouseout="($event.target as HTMLElement).style.background='white'">Cancel</button>
          <button 
            @click="savePermissions" 
            :disabled="!!(role?.is_builtin || saving)"
            style="padding:10px 20px; border:none; background:#3b82f6; color:white; border-radius:6px; cursor:pointer; font-weight:500; transition:all 0.2s; display:flex; align-items:center; gap:8px;"
            :style="{ opacity: (role?.is_builtin || saving) ? 0.5 : 1, cursor: (role?.is_builtin || saving) ? 'not-allowed' : 'pointer' }"
            @mouseover="!role?.is_builtin && !saving && (($event.target as HTMLElement).style.background='#2563eb')"
             @mouseout="!role?.is_builtin && !saving && (($event.target as HTMLElement).style.background='#3b82f6')"
          >
            <svg v-if="saving" style="width:16px; height:16px; fill:currentColor; animation:spin 1s linear infinite;" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            {{ saving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { authHeaders } from '@/lib/auth'
import { authenticatedApiRequest } from '@/lib/api'
import { useToasts } from '@/lib/toasts'
import type { Role } from '@/types/admin-modals'

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  is_active: number;
}

interface Props {
  show: boolean
  role: Role | null
}

interface Emits {
  (e: 'close'): void
  (e: 'saved'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const toastState = useToasts()

const loading = ref(false)
const saving = ref(false)
const availablePermissions = ref<Permission[]>([])
const selectedPermissions = ref<string[]>([])

const togglePermission = (permissionId: string) => {
  if (props.role?.is_builtin) return
  
  const index = selectedPermissions.value.indexOf(permissionId)
  if (index > -1) {
    selectedPermissions.value.splice(index, 1)
  } else {
    selectedPermissions.value.push(permissionId)
  }
}

const getPermissionsByCategory = (categories: string | string[]) => {
  const categoryArray = Array.isArray(categories) ? categories : [categories];
  return availablePermissions.value.filter(permission => 
    categoryArray.includes(permission.category)
  );
};

const loadPermissions = async () => {
  if (!props.role) return
  
  loading.value = true
  try {
    // Load available permissions
    const permissionsRes = await authenticatedApiRequest('/v1/admin/permissions')
    
    if (permissionsRes.ok) {
      availablePermissions.value = await permissionsRes.json()
    }
    
    // Load current role permissions
    const rolePermissionsRes = await authenticatedApiRequest(`/v1/admin/roles/${props.role.id}/permissions`)
    
    if (rolePermissionsRes.ok) {
      selectedPermissions.value = await rolePermissionsRes.json()
    }
  } catch (error) {
    console.error('Failed to load permissions:', error)
    toastState.push({ kind: 'error', text: 'Failed to load permissions' })
  } finally {
    loading.value = false
  }
}

const savePermissions = async () => {
  if (!props.role || props.role.is_builtin) return
  
  saving.value = true
  try {
    const res = await authenticatedApiRequest(`/v1/admin/roles/${props.role.id}/permissions`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ permissions: selectedPermissions.value })
    })
    
    if (res.ok) {
      toastState.push({ kind: 'success', text: 'Permissions updated successfully' })
      emit('saved')
      emit('close')
    } else {
      const errorData = await res.json().catch(() => ({}))
      if (errorData.error?.code === 'BUILTIN_ROLE_PERMISSIONS_CANNOT_BE_MODIFIED') {
        toastState.push({ kind: 'error', text: 'Built-in role permissions cannot be modified' })
      } else {
        toastState.push({ kind: 'error', text: 'Failed to update permissions' })
      }
    }
  } catch (error) {
    console.error('Failed to save permissions:', error)
    toastState.push({ kind: 'error', text: 'Error saving permissions' })
  } finally {
    saving.value = false
  }
}

// Watch for role changes to reload permissions
watch(() => props.role, (newRole) => {
  if (newRole && props.show) {
    loadPermissions()
  }
})

// Load permissions when modal opens
watch(() => props.show, (show) => {
  if (show && props.role) {
    loadPermissions()
  }
})
</script>