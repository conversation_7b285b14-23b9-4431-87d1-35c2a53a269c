<template>
  <div v-if="modelValue" style="position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); display:flex; align-items:center; justify-content:center; z-index:1000;">
    <div style="background:#fff; border-radius:8px; padding:24px; width:500px; max-height:80vh; overflow-y:auto;">
      <h3 style="margin:0 0 16px 0; font-size:18px; font-weight:600;">{{ modalTitle }}</h3>
      
      <div :style="MODAL_STYLES.container">
        <!-- Action Name Field -->
        <div :style="MODAL_STYLES.fieldGroup">
          <label :style="MODAL_STYLES.label">Permission Name *</label>
          <input 
            v-model="formData.name" 
            placeholder="Enter permission name" 
            :style="Object.assign({}, MODAL_STYLES.input, { borderColor: errors.name ? '#dc2626' : '#e5e7eb' })"
            @blur="validateForm"
          />
          <small v-if="errors.name" :style="MODAL_STYLES.error">{{ errors.name }}</small>
        </div>

        <!-- Description Field -->
        <div :style="MODAL_STYLES.fieldGroup">
          <label :style="MODAL_STYLES.label">Description</label>
          <textarea 
            v-model="formData.description" 
            placeholder="Enter permission description (optional)" 
            :style="Object.assign({}, MODAL_STYLES.textarea, { borderColor: errors.description ? '#dc2626' : '#e5e7eb' })"
            @blur="validateForm"
          ></textarea>
          <small v-if="errors.description" :style="MODAL_STYLES.error">{{ errors.description }}</small>
        </div>

        <!-- Resource Type Field -->
        <div :style="MODAL_STYLES.fieldGroup">
          <label :style="MODAL_STYLES.label">Resource Type *</label>
          <select 
            v-model="formData.resource_type" 
            :style="Object.assign({}, MODAL_STYLES.input, { borderColor: errors.resource_type ? '#dc2626' : '#e5e7eb' })"
            @blur="validateForm"
          >
            <option value="">Select resource type</option>
            <option value="file">File</option>
            <option value="folder">Folder</option>
            <option value="system">System</option>
            <option value="user">User</option>
            <option value="group">Group</option>
            <option value="role">Role</option>
          </select>
          <small v-if="errors.resource_type" :style="MODAL_STYLES.error">{{ errors.resource_type }}</small>
        </div>

        <!-- Edit Mode: Additional Info -->
        <div v-if="mode === 'edit' && permission" :style="MODAL_STYLES.fieldGroup">
          <label :style="MODAL_STYLES.label">Permission Information</label>
          <div style="display:flex; gap:8px; align-items:center;">
            <span style="padding:4px 8px; border-radius:12px; font-size:12px; font-weight:500; background:#f3f4f6; color:#374151; border:1px solid #9ca3af;">
              ID: {{ permission.id }}
            </span>
            <span v-if="permission.created_at" style="color:#6b7280; font-size:12px;">
              Created {{ formatDate(permission.created_at) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div :style="MODAL_STYLES.footer">
        <button 
          @click="cancel" 
          :style="MODAL_STYLES.buttonCancel"
          :disabled="isSubmitting"
        >
          Cancel
        </button>
        <button 
          @click="submit" 
          :disabled="!isValid || isSubmitting" 
          :style="`${MODAL_STYLES.buttonPrimary} opacity:${(isValid && !isSubmitting) ? 1 : 0.6};`"
        >
          {{ isSubmitting ? 'Saving...' : (mode === 'add' ? 'Create Permission' : 'Update Permission') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import type { PermissionModalProps, PermissionModalEmits, PermissionFormData, ValidationErrors } from '@/types/admin-modals'
import { MODAL_STYLES } from '@/types/admin-modals'
import { useBaseModal } from '@/composables/useBaseModal'

const props = withDefaults(defineProps<PermissionModalProps>(), {
  mode: 'add'
})

const emit = defineEmits<PermissionModalEmits>()

const modalTitle = computed(() => 
  props.mode === 'add' ? 'Create Permission' : 'Edit Permission'
)

function formatDate(dateString: string) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString()
}

// Validation function
const validatePermissionData = (data: PermissionFormData): Record<string, string> => {
  const errs: Record<string, string> = {}
  
  if (!data.name?.trim()) {
    errs.name = 'Permission name is required'
  } else if (data.name.trim().length < 2) {
    errs.name = 'Permission name must be at least 2 characters long'
  } else if (data.name.trim().length > 50) {
    errs.name = 'Permission name must be less than 50 characters'
  }
  
  if (!data.resource_type?.trim()) {
    errs.resource_type = 'Resource type is required'
  }
  
  if (data.description && data.description.trim().length > 200) {
    errs.description = 'Description must be less than 200 characters'
  }
  
  return errs
}

// Load action data function
const loadPermissionData = () => {
  if (props.mode === 'edit' && props.permission) {
    formData.value = {
      name: props.permission.name,
      description: props.permission.description || '',
      resource_type: props.permission.resource_type
    }
  }
}

// Use base modal composable
const {
  show,
  formData,
  errors,
  isSubmitting,
  isValid,
  validateForm,
  cancel,
  submit
} = useBaseModal<PermissionFormData>(props, emit, {
  initialData: () => ({
    name: '',
    description: '',
    resource_type: ''
  }),
  validate: validatePermissionData,
  onLoadData: loadPermissionData
})

// Watch for prop changes to reload data
watch([() => props.mode, () => props.permission], loadPermissionData, { immediate: true })
</script>