# Use Node.js 18 Alpine as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN pnpm run build

# Expose the port
EXPOSE 3000

# Set environment variables
ENV VITE_APP_TITLE="Agrizy File Browser"

# Start the development server (for production, you might want to use a static server)
CMD ["pnpm", "run", "dev", "--host", "0.0.0.0", "--port", "3000"]