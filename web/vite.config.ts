import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig(({ mode }) => ({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    // Only use proxy in development when VITE_API_BASE_URL is not set
    proxy: mode === 'development' && !process.env.VITE_API_BASE_URL ? {
      '/v1': {
        target: process.env.VITE_API_PROXY_TARGET || 'http://agrizy-api:3030',
        changeOrigin: true,
        configure: (proxy, options) => {
          // Use Docker container hostname for internal communication
          proxy.on('error', (err, req, res) => {
            console.log('Proxy error, check Docker network connectivity...')
          })
        }
      }
    } : undefined
  }
}))
