# Repository Guidelines

## Project Structure & Module Organization
- `api/`: Fastify + TypeScript server (`src/server.ts`, routes in `src/routes/`, storage providers in `src/providers/`).
- `web/`: Vue 3 + Vite app (`src/views/`, `src/components/`, shared utils in `src/lib/`).
- `docs/`: Product and implementation specs.
- Keep modules small and reusable; prefer shared libs over duplication.

## Build, Test, and Development Commands
- API (http://localhost:4000):
  - `cd api && pnpm i`
  - Dev: `pnpm dev`
  - Build: `pnpm build` → outputs `dist/`
  - Start (prod): `pnpm start`
- Web (http://localhost:5173):
  - `cd web && pnpm i`
  - Dev: `pnpm dev` (proxies `/v1` → `http://localhost:4000`)
  - Build: `pnpm build`
  - Preview: `pnpm preview`

## Coding Style & Naming Conventions
- Language: TypeScript + ESM; 2-space indentation.
- Vue components: PascalCase filenames (e.g., `FileBrowser.vue`).
- Modules/utilities: lowercase filenames (e.g., `files.ts`).
- Names: camelCase for vars/functions, PascalCase for classes/components.
- Consistency: match existing layouts, fonts, colors; extract shared UI/logic to `web/src/lib/` or base components.

## Testing Guidelines
- No test runner is configured yet. When adding tests:
  - Web: Vitest + Vue Test Utils; name `*.spec.ts` next to components.
  - API: Vitest or Node test runner; name `*.spec.ts` next to source.
  - Aim for focused, fast tests; prefer unit tests for routes/providers.

## Commit & Pull Request Guidelines
- Commits: Prefer Conventional Commits (e.g., `feat(api):`, `fix(web):`, `docs:`). Keep PRs small and scoped.
- PRs: clear description, steps to test, linked issues, and screenshots/GIFs for UI changes. Note API/ENV impacts.
- Always check for duplicate code/components and consolidate before submitting.

## Security & Configuration Tips
- API reads config from env (`api/src/env.ts`): `PORT`, `HOST`, `FS_ROOT`, `S3_*`.
- Use a local `.env` in `api/` and do not commit secrets.
- Health: `GET /v1/health`. CORS enabled for dev. Keep provider credentials minimal and scoped.

