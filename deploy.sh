#!/bin/bash

# Deployment script for Agrizy File Browser
set -e

echo "🚀 Starting deployment to EC2 instance..."

# Variables
INSTANCE_ID="i-0f27937385fb3bdad"
REMOTE_PATH="/home/<USER>/apps/file-browser"
LOCAL_PATH="/Users/<USER>/home/<USER>/agrizy/file-browser"

echo "📦 Creating deployment package..."

# Create temporary deployment directory
TEMP_DIR=$(mktemp -d)
echo "Temporary directory: $TEMP_DIR"

# Copy all files except node_modules, .git, and other unnecessary files
rsync -av --exclude='node_modules' \
          --exclude='.git' \
          --exclude='.DS_Store' \
          --exclude='*.pid' \
          --exclude='.claude' \
          --exclude='.trae' \
          --exclude='clear-recent-sources.js' \
          --exclude='test-google-signin.html' \
          --exclude='docs' \
          --exclude='scripts' \
          "$LOCAL_PATH/" "$TEMP_DIR/"

echo "📤 Transferring files to EC2 instance..."

# Create deployment archive
cd "$TEMP_DIR"
tar -czf ../file-browser-deploy.tar.gz .

# Transfer archive to EC2
echo "Uploading deployment archive..."
aws ssm send-command \
    --instance-ids "$INSTANCE_ID" \
    --document-name "AWS-RunShellScript" \
    --parameters 'commands=["mkdir -p ~/apps/file-browser"]' \
    --output text

# Copy the archive using SSM
aws s3 cp "$TEMP_DIR/../file-browser-deploy.tar.gz" s3://agrizy-temp-deploy/

# Download and extract on EC2
aws ssm send-command \
    --instance-ids "$INSTANCE_ID" \
    --document-name "AWS-RunShellScript" \
    --parameters 'commands=[
        "cd ~/apps/file-browser",
        "aws s3 cp s3://agrizy-temp-deploy/file-browser-deploy.tar.gz .",
        "tar -xzf file-browser-deploy.tar.gz",
        "rm file-browser-deploy.tar.gz",
        "ls -la"
    ]' \
    --output text

# Clean up
rm -rf "$TEMP_DIR"
rm -f "$TEMP_DIR/../file-browser-deploy.tar.gz"

echo "✅ Files transferred successfully!"