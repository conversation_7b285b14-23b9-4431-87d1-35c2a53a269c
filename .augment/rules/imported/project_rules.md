---
type: "manual"
---

# Agrizy File Browser - Project Rules & Configuration

This document serves as the authoritative reference for all project configuration, setup procedures, and development standards.

## 🚀 Required Ports

### Mandatory Port Assignments
- **Frontend (Web)**: Port `3000` - MANDATORY, NO FALLBACK
- **Backend (API)**: Port `3030` - MANDATORY, NO FALLBACK

### Port Management Protocol

#### Pre-startup Verification
Before starting any service, MUST verify and terminate existing processes on required ports:

```bash
# Check for processes on required ports
lsof -ti:3000 -ti:3030

# Terminate existing processes if found
kill -9 $(lsof -ti:3000) 2>/dev/null || true
kill -9 $(lsof -ti:3030) 2>/dev/null || true
```

#### Error Handling for Port Conflicts
- **NEVER** fallback to alternative ports
- **ALWAYS** terminate conflicting processes
- **FAIL FAST** if ports cannot be freed
- Log clear error messages indicating which port is blocked

#### Service Startup Sequence
1. Verify port availability
2. Terminate conflicting processes
3. Start backend on port 3030
4. Start frontend on port 3000
5. Verify both services are accessible

## 📦 Approved Frameworks & Versions

### Frontend Stack
- **Vue.js**: `^3.5.21`
- **Vite**: `^5.4.19`
- **TypeScript**: `^5.9.2`
- **Vue Router**: `^4.x`
- **Pinia**: Latest stable
- **Tailwind CSS**: Latest stable

### Backend Stack
- **Node.js**: `>=18.0.0`
- **TypeScript**: `^5.9.2`
- **Express.js**: Latest stable
- **SQLite**: `^3.x`
- **Better-SQLite3**: Latest stable

### Development Tools
- **Package Manager**: `pnpm` (preferred) or `npm`
- **Linting**: ESLint with Vue/TypeScript configs
- **Formatting**: Prettier
- **Git Hooks**: Husky (optional)

## 🛠 Development Environment Specifications

### System Requirements
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher
- **pnpm**: Version 8.0.0 or higher (recommended)
- **Git**: Latest stable version

### IDE Configuration
- **Recommended**: VS Code with Vue Language Features (Volar)
- **Required Extensions**:
  - Vue Language Features (Volar)
  - TypeScript Vue Plugin (Volar)
  - ESLint
  - Prettier

### Environment Variables
```bash
# Backend (.env)
PORT=3030
DB_PATH=./data/app.db
NODE_ENV=development
JWT_SECRET=your-secret-key
CORS_ORIGIN=http://localhost:3000

# Frontend (.env)
VITE_API_BASE_URL=http://localhost:3030
VITE_APP_TITLE=Agrizy File Browser
```

## 🏃‍♂️ Runtime Requirements

### Backend Runtime
- **Memory**: Minimum 512MB, Recommended 1GB
- **CPU**: Single core sufficient for development
- **Disk**: 100MB for application, additional space for file storage
- **Database**: SQLite file-based storage

### Frontend Runtime
- **Memory**: 256MB for build process
- **CPU**: Single core sufficient
- **Disk**: 50MB for built assets

### Network Requirements
- **CORS**: Backend must allow frontend origin (localhost:3000)
- **API Endpoints**: All backend routes prefixed with `/v1/`
- **Static Assets**: Served from frontend on port 3000

## 📁 Project Structure Standards

### Backend Structure (`/api`)
```
api/
├── src/
│   ├── routes/          # API route handlers
│   ├── providers/       # External service providers
│   ├── utils/          # Utility functions
│   ├── db.ts           # Database configuration
│   ├── server.ts       # Express server setup
│   └── types.d.ts      # TypeScript type definitions
├── data/               # SQLite database files
├── package.json
└── tsconfig.json
```

### Frontend Structure (`/web`)
```
web/
├── src/
│   ├── components/     # Reusable Vue components
│   ├── views/         # Page-level components
│   ├── layouts/       # Layout components
│   ├── composables/   # Vue composition functions
│   ├── types/         # TypeScript type definitions
│   ├── lib/          # Utility libraries
│   ├── router.ts     # Vue Router configuration
│   └── main.ts       # Application entry point
├── public/           # Static assets
├── package.json
└── vite.config.ts
```

## 💻 Code Quality & Architecture Standards

### Code Simplicity Principles

#### Keep It Simple (KISS)
- **Single Responsibility**: Each function/component should have one clear purpose
- **Minimal Complexity**: Avoid over-engineering solutions
- **Readable Code**: Write code that explains itself without excessive comments
- **Clear Naming**: Use descriptive names for variables, functions, and components

#### No Code Duplication (DRY)
- **Shared Utilities**: Extract common logic into utility functions
- **Reusable Components**: Create generic components for repeated UI patterns
- **Constants**: Define constants once and import where needed
- **Configuration**: Centralize configuration in dedicated files

```typescript
// ❌ BAD: Duplicated validation logic
function validateEmail(email: string) {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}
function validateUserEmail(email: string) {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

// ✅ GOOD: Single validation utility
// utils/validation.ts
export const validateEmail = (email: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};
```

### Modular Architecture

#### Component Modularity
- **Atomic Design**: Build components from atoms → molecules → organisms
- **Props Interface**: Well-defined props with TypeScript interfaces
- **Composition over Inheritance**: Use Vue slots and composition patterns
- **Feature-based Organization**: Group related components by feature

```vue
<!-- ✅ GOOD: Modular component structure -->
<template>
  <BaseCard>
    <CardHeader :title="title" :subtitle="subtitle" />
    <CardContent>
      <slot />
    </CardContent>
    <CardActions>
      <slot name="actions" />
    </CardActions>
  </BaseCard>
</template>
```

#### Backend Modularity
- **Route Separation**: Separate routes by feature/domain
- **Service Layer**: Business logic in dedicated service classes
- **Middleware**: Reusable middleware for common operations
- **Database Abstraction**: Repository pattern for data access

```typescript
// ✅ GOOD: Modular backend structure
// routes/users.ts
import { UserService } from '../services/UserService';
import { authMiddleware } from '../middleware/auth';

router.get('/users', authMiddleware, async (req, res) => {
  const users = await UserService.getAllUsers();
  res.json(users);
});
```

### Consistent Patterns & Layouts

#### Frontend Consistency
- **Component Naming**: PascalCase for components, camelCase for props
- **File Structure**: Consistent file organization across features
- **State Management**: Uniform Pinia store patterns
- **API Calls**: Standardized composables for data fetching

```typescript
// ✅ GOOD: Consistent composable pattern
// composables/useUsers.ts
export const useUsers = () => {
  const users = ref<User[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const fetchUsers = async () => {
    loading.value = true;
    try {
      users.value = await api.get('/users');
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  return { users, loading, error, fetchUsers };
};
```

#### Backend Consistency
- **Error Handling**: Standardized error response format
- **Response Structure**: Consistent API response patterns
- **Validation**: Uniform input validation approach
- **Logging**: Consistent logging format and levels

```typescript
// ✅ GOOD: Consistent error handling
export const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  const response = {
    success: false,
    message: err.message,
    code: err.name,
    timestamp: new Date().toISOString()
  };
  
  res.status(500).json(response);
};
```

#### Layout Consistency
- **Page Templates**: Standardized page layout components
- **Navigation Patterns**: Consistent navigation behavior
- **Form Layouts**: Uniform form styling and validation
- **Data Display**: Consistent table, list, and card layouts

```vue
<!-- ✅ GOOD: Consistent page layout -->
<template>
  <PageLayout>
    <template #header>
      <PageHeader :title="pageTitle" :breadcrumbs="breadcrumbs" />
    </template>
    
    <template #content>
      <slot />
    </template>
    
    <template #actions>
      <slot name="actions" />
    </template>
  </PageLayout>
</template>
```

### Code Organization Standards

#### File Naming Conventions
- **Components**: PascalCase (e.g., `UserProfile.vue`)
- **Composables**: camelCase with 'use' prefix (e.g., `useUserData.ts`)
- **Utilities**: camelCase (e.g., `formatDate.ts`)
- **Types**: PascalCase with descriptive names (e.g., `UserTypes.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)

#### Import/Export Patterns
```typescript
// ✅ GOOD: Consistent import organization
// 1. External libraries
import { ref, computed } from 'vue';
import axios from 'axios';

// 2. Internal utilities
import { formatDate } from '@/utils/date';
import { validateEmail } from '@/utils/validation';

// 3. Types
import type { User, UserRole } from '@/types/User';

// 4. Components
import BaseButton from '@/components/BaseButton.vue';
```

#### Documentation Standards
- **JSDoc Comments**: For complex functions and public APIs
- **README Files**: For each major feature/module
- **Type Definitions**: Comprehensive TypeScript interfaces
- **Code Comments**: Only when business logic is complex

```typescript
/**
 * Validates user permissions for a specific action
 * @param user - The user object with roles
 * @param action - The action to validate
 * @returns Promise<boolean> - Whether user has permission
 */
export const validateUserPermission = async (
  user: User, 
  action: string
): Promise<boolean> => {
  // Implementation...
};
```

### Performance & Optimization

#### Frontend Optimization
- **Lazy Loading**: Route-based code splitting
- **Component Optimization**: Use `defineAsyncComponent` for heavy components
- **State Optimization**: Minimize reactive state, use `shallowRef` when appropriate
- **Bundle Analysis**: Regular bundle size monitoring

#### Backend Optimization
- **Database Queries**: Optimize N+1 queries, use proper indexing
- **Caching**: Implement caching for frequently accessed data
- **Memory Management**: Avoid memory leaks in long-running processes
- **Response Optimization**: Minimize payload size, use compression

### Testing Standards

#### Unit Testing
- **Test Coverage**: Minimum 80% coverage for critical business logic
- **Test Naming**: Descriptive test names explaining the scenario
- **Test Structure**: Arrange-Act-Assert pattern
- **Mocking**: Mock external dependencies consistently

#### Integration Testing
- **API Testing**: Test all API endpoints with various scenarios
- **Component Testing**: Test component behavior and user interactions
- **E2E Testing**: Critical user flows must have E2E coverage

## 🎨 UI/UX Standards & Design Guidelines

### Design System Principles

#### Visual Hierarchy
- **Typography Scale**: Use consistent font sizes (12px, 14px, 16px, 18px, 24px, 32px)
- **Color Palette**: Maintain consistent brand colors with proper contrast ratios
- **Spacing System**: Use 4px grid system (4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px)
- **Component Consistency**: Reuse components across the application

#### Layout Standards
- **Responsive Design**: Mobile-first approach with breakpoints:
  - Mobile: 320px - 768px
  - Tablet: 768px - 1024px
  - Desktop: 1024px+
- **Grid System**: 12-column grid with consistent gutters
- **Container Widths**: Max-width 1200px for content areas
- **Navigation**: Consistent header/sidebar navigation patterns

### Accessibility Requirements (WCAG 2.1 AA Compliance)

#### Color & Contrast
- **Minimum Contrast Ratio**: 4.5:1 for normal text, 3:1 for large text
- **Color Independence**: Never rely solely on color to convey information
- **Focus Indicators**: Visible focus states for all interactive elements
- **Color Blindness**: Test with color blindness simulators

#### Keyboard Navigation
- **Tab Order**: Logical tab sequence through all interactive elements
- **Keyboard Shortcuts**: Standard shortcuts (Ctrl+S for save, Esc to close modals)
- **Skip Links**: "Skip to main content" for screen readers
- **Focus Management**: Proper focus handling in modals and dynamic content

#### Screen Reader Support
- **Semantic HTML**: Use proper heading hierarchy (h1, h2, h3...)
- **ARIA Labels**: Descriptive labels for all form controls and buttons
- **Alt Text**: Meaningful alternative text for all images
- **Live Regions**: ARIA live regions for dynamic content updates
- **Landmark Roles**: Proper use of main, nav, aside, footer landmarks

#### Form Accessibility
- **Label Association**: All form inputs must have associated labels
- **Error Handling**: Clear, descriptive error messages
- **Required Fields**: Visual and programmatic indication of required fields
- **Input Validation**: Real-time validation with accessible feedback

### User Experience Best Practices

#### Performance Standards
- **Page Load Time**: < 3 seconds for initial load
- **Time to Interactive**: < 5 seconds
- **Core Web Vitals**:
  - Largest Contentful Paint (LCP): < 2.5s
  - First Input Delay (FID): < 100ms
  - Cumulative Layout Shift (CLS): < 0.1
- **Bundle Size**: JavaScript bundles < 250KB gzipped

#### Interaction Design
- **Loading States**: Show loading indicators for operations > 200ms
- **Error Handling**: User-friendly error messages with recovery options
- **Confirmation Dialogs**: Confirm destructive actions (delete, overwrite)
- **Undo Functionality**: Provide undo for reversible actions
- **Progressive Disclosure**: Show advanced options only when needed

#### Content Guidelines
- **Microcopy**: Clear, concise, and helpful text throughout the interface
- **Empty States**: Meaningful empty state messages with next steps
- **Success Messages**: Positive feedback for completed actions
- **Help Text**: Contextual help without cluttering the interface

### Component Design Standards

#### Buttons
- **Primary Button**: Single primary action per screen
- **Secondary Buttons**: Supporting actions with lower visual weight
- **Button States**: Normal, hover, active, disabled, loading
- **Button Sizes**: Small (32px), Medium (40px), Large (48px)
- **Icon Buttons**: 24px icons with 40px touch targets

#### Forms
- **Input Fields**: Consistent styling with clear labels and placeholders
- **Validation**: Inline validation with clear error states
- **Field Groups**: Logical grouping with proper spacing
- **Required Indicators**: Consistent marking of required fields

#### Navigation
- **Breadcrumbs**: Show current location in deep hierarchies
- **Active States**: Clear indication of current page/section
- **Menu Structure**: Logical grouping with clear categories
- **Search**: Prominent search functionality with autocomplete

#### Data Display
- **Tables**: Sortable columns, pagination, and filtering
- **Lists**: Consistent item spacing and hover states
- **Cards**: Consistent card layouts with clear actions
- **Status Indicators**: Clear visual status communication

### Mobile-First Design

#### Touch Targets
- **Minimum Size**: 44px × 44px for all touch targets
- **Spacing**: Minimum 8px between touch targets
- **Gesture Support**: Swipe, pinch-to-zoom where appropriate

#### Mobile Navigation
- **Hamburger Menu**: Collapsible navigation for mobile
- **Bottom Navigation**: Primary actions accessible with thumb
- **Pull-to-Refresh**: Standard mobile refresh pattern

### Design Tokens

#### Colors
```css
/* Primary Colors */
--color-primary-50: #f0f9ff;
--color-primary-500: #3b82f6;
--color-primary-900: #1e3a8a;

/* Semantic Colors */
--color-success: #10b981;
--color-warning: #f59e0b;
--color-error: #ef4444;
--color-info: #3b82f6;

/* Neutral Colors */
--color-gray-50: #f9fafb;
--color-gray-500: #6b7280;
--color-gray-900: #111827;
```

#### Typography
```css
/* Font Families */
--font-sans: 'Inter', system-ui, sans-serif;
--font-mono: 'JetBrains Mono', monospace;

/* Font Sizes */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
```

#### Spacing
```css
/* Spacing Scale */
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
--space-12: 3rem;    /* 48px */
--space-16: 4rem;    /* 64px */
```

### Testing Requirements

#### Accessibility Testing
- **Automated Testing**: Use axe-core for automated accessibility testing
- **Manual Testing**: Keyboard-only navigation testing
- **Screen Reader Testing**: Test with NVDA, JAWS, or VoiceOver
- **Color Contrast**: Use tools like WebAIM Contrast Checker

#### Cross-Browser Testing
- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest 2 versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile
- **Feature Detection**: Use progressive enhancement for newer features

#### Performance Testing
- **Lighthouse Audits**: Regular performance, accessibility, and SEO audits
- **Real Device Testing**: Test on actual mobile devices
- **Network Throttling**: Test on slow 3G connections

### Implementation Guidelines

#### CSS Architecture
- **Utility-First**: Use Tailwind CSS for consistent styling
- **Component Styles**: Scoped styles for complex components
- **CSS Custom Properties**: Use CSS variables for theming
- **BEM Methodology**: For custom CSS class naming

#### Vue.js Specific
- **Composition API**: Use Composition API for complex logic
- **Props Validation**: Define proper prop types and validation
- **Emit Events**: Use typed events for component communication
- **Slots**: Use slots for flexible component composition

#### State Management
- **Pinia Stores**: Organize state by feature domains
- **Computed Properties**: Use for derived state
- **Watchers**: Minimal use, prefer computed properties

## 🔧 Setup & Development Commands

### Initial Setup
```bash
# Install dependencies
cd api && pnpm install
cd ../web && pnpm install

# Setup database
cd ../api && npm run migrate
```

### Development Workflow
```bash
# Terminal 1: Start backend
cd api
npm run dev  # Starts on port 3030

# Terminal 2: Start frontend
cd web
npm run dev  # Starts on port 3000
```

### Build & Production
```bash
# Build frontend
cd web && npm run build

# Build backend
cd api && npm run build
```

## 🚨 Critical Rules

### Technical Requirements
1. **Port Enforcement**: NEVER use ports other than 3000/3030
2. **No Auto-fallback**: Services must fail if required ports are unavailable
3. **Process Cleanup**: Always terminate existing processes before startup
4. **Version Consistency**: Use exact versions specified in this document
5. **Environment Isolation**: Use separate .env files for different environments
6. **Database Integrity**: Never commit database files to version control
7. **CORS Configuration**: Backend must explicitly allow frontend origin
8. **API Versioning**: All API routes must be prefixed with `/v1/`

### UI/UX Compliance Requirements
9. **Accessibility Compliance**: ALL components must meet WCAG 2.1 AA standards
10. **Mobile-First Design**: Design and develop for mobile devices first
11. **Performance Standards**: Page load times must be under 3 seconds
12. **Keyboard Navigation**: All interactive elements must be keyboard accessible
13. **Color Contrast**: Minimum 4.5:1 contrast ratio for all text
14. **Touch Targets**: Minimum 44px × 44px for all touch interactions
15. **Error Handling**: Provide clear, actionable error messages
16. **Loading States**: Show loading indicators for operations > 200ms
17. **Focus Management**: Proper focus handling in modals and dynamic content
18. **Semantic HTML**: Use proper HTML5 semantic elements and ARIA labels
19. **Design Tokens**: Use consistent spacing, typography, and color systems
20. **Component Reusability**: Create reusable components following design system

### Code Quality & Architecture Requirements
21. **No Code Duplication**: Extract common logic into reusable utilities and components
22. **Single Responsibility**: Each function/component must have one clear purpose
23. **Consistent Naming**: Follow established naming conventions (PascalCase for components, camelCase for functions)
24. **Modular Architecture**: Organize code by feature with clear separation of concerns
25. **TypeScript Compliance**: All code must use proper TypeScript types and interfaces
26. **Import Organization**: Follow consistent import order (external → internal → types → components)
27. **Error Handling**: Implement standardized error handling patterns across frontend and backend
28. **Testing Coverage**: Minimum 80% test coverage for critical business logic
29. **Performance Optimization**: Implement lazy loading, code splitting, and bundle optimization
30. **Documentation Standards**: Use JSDoc for complex functions and maintain README files for features

## 🔍 Troubleshooting

### Port Conflicts
```bash
# Find process using port
lsof -i :3000
lsof -i :3030

# Kill specific process
kill -9 <PID>

# Kill all processes on ports
kill -9 $(lsof -ti:3000,3030)
```

### Common Technical Issues
- **EADDRINUSE**: Port already in use - follow port cleanup protocol
- **CORS Errors**: Verify backend CORS_ORIGIN matches frontend URL
- **Module Not Found**: Run `pnpm install` in respective directory
- **Database Locked**: Ensure no other processes are accessing SQLite file

### UI/UX Troubleshooting

#### Accessibility Issues
```bash
# Run accessibility audit
npm run test:a11y

# Check color contrast
# Use browser dev tools or online contrast checkers
# Minimum ratio: 4.5:1 for normal text, 3:1 for large text
```

#### Performance Issues
```bash
# Run Lighthouse audit
npm run audit:lighthouse

# Check bundle size
npm run analyze:bundle

# Monitor Core Web Vitals
# LCP < 2.5s, FID < 100ms, CLS < 0.1
```

#### Responsive Design Issues
- **Layout Breaks**: Test all breakpoints (320px, 768px, 1024px+)
- **Touch Targets**: Ensure minimum 44px × 44px on mobile
- **Text Readability**: Check font sizes are readable on small screens
- **Navigation**: Verify mobile navigation is accessible

#### Component Issues
- **Focus States**: All interactive elements must have visible focus indicators
- **Loading States**: Operations > 200ms must show loading feedback
- **Error States**: Provide clear, actionable error messages
- **Empty States**: Show helpful guidance when no content is available

#### Cross-Browser Compatibility
- **Modern Browsers**: Test on Chrome, Firefox, Safari, Edge (latest 2 versions)
- **Mobile Browsers**: Test on iOS Safari and Chrome Mobile
- **Feature Detection**: Use progressive enhancement for newer CSS/JS features
- **Polyfills**: Include necessary polyfills for older browser support

---

**Last Updated**: $(date)
**Maintainer**: Development Team
**Version**: 3.0.0
**Changelog**: 
- v3.0.0: Added comprehensive code quality & architecture standards, modularity principles, and consistency patterns
- v2.0.0: Added comprehensive UI/UX standards, accessibility requirements, and design guidelines
- v1.0.0: Initial project rules and configuration