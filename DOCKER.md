# Docker Setup for Agrizy File Browser

This document provides instructions for running the Agrizy File Browser application using Docker Compose.

## Prerequisites

- Docker Engine 20.10.0 or later
- Docker Compose 2.0.0 or later

## Quick Start

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd agrizy-file-browser
   ```

2. **Build and start all services**:
   ```bash
   docker-compose up --build
   ```

3. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3030

## Services Overview

### Frontend (web)
- **Port**: 3000
- **Technology**: Vue.js + Vite
- **Build**: Multi-stage build with Node.js 18 Alpine

### Backend (api)
- **Port**: 3030
- **Technology**: Node.js + TypeScript + Fastify
- **Database**: MySQL 8.0
- **Build**: Multi-stage build with Node.js 18 Alpine

### Database (mysql)
- **Type**: MySQL 8.0 server
- **Port**: 3306
- **Storage**: Persistent Docker volume (`mysql_data`)
- **Credentials**: db_user/jehREviconZygdan
- **Database**: agrizy_files_db

## Docker Commands

### Start services in background
```bash
docker-compose up -d
```

### View logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f web
docker-compose logs -f api
```

### Stop services
```bash
docker-compose down
```

### Rebuild and restart
```bash
docker-compose down
docker-compose up --build
```

### Remove volumes (⚠️ This will delete all data)
```bash
docker-compose down -v
```

## Environment Variables

### Backend (API)
- `NODE_ENV`: Set to `production`
- `PORT`: API server port (3030)
- `DB_HOST`: MySQL server hostname (mysql)
- `DB_USER`: MySQL username (db_user)
- `DB_PASSWORD`: MySQL password
- `DB_NAME`: MySQL database name (agrizy_files_db)
- `DB_PORT`: MySQL port (3306)
- `DB_SSL`: SSL connection flag (false for local)
- `CORS_ORIGIN`: Frontend URL for CORS
- `JWT_SECRET`: Secret key for JWT tokens (⚠️ Change in production)

### Frontend (Web)
- `VITE_API_BASE_URL`: Backend API URL
- `VITE_APP_TITLE`: Application title

## Data Persistence

The MySQL database is stored in a Docker volume named `mysql_data`. This ensures that:
- Data persists between container restarts
- Database schema is automatically initialized on first run
- Database files are not lost when containers are removed
- RBAC permissions are set up automatically via init scripts

## Development vs Production

### Current Setup (Development-friendly)
- Frontend runs in development mode with hot reload
- Backend runs the built TypeScript code
- Suitable for development and testing

### For Production
Consider these modifications:
1. **Frontend**: Build static files and serve with nginx
2. **Security**: Change JWT_SECRET to a secure random value
3. **Environment**: Set proper environment variables
4. **Volumes**: Consider using named volumes or bind mounts for data
5. **Networking**: Use proper reverse proxy setup

## Troubleshooting

### Port Conflicts
If ports 3000 or 3030 are already in use:
```bash
# Check what's using the ports
lsof -ti:3000 -ti:3030

# Kill existing processes
kill -9 $(lsof -ti:3000) 2>/dev/null || true
kill -9 $(lsof -ti:3030) 2>/dev/null || true
```

### Database Issues
If you encounter database-related errors:
```bash
# Check database volume
docker volume inspect agrizy-file-browser_mysql_data

# Access MySQL container
docker-compose exec mysql mysql -u db_user -p agrizy_files_db

# Check MySQL logs
docker-compose logs mysql
```

### Build Issues
If builds fail:
```bash
# Clean build cache
docker system prune -a

# Rebuild without cache
docker-compose build --no-cache
```

## Security Notes

⚠️ **Important for Production**:
1. Change the `JWT_SECRET` environment variable
2. Use proper SSL/TLS certificates
3. Configure proper firewall rules
4. Regular security updates for base images
5. Use secrets management for sensitive data

## File Structure

```
agrizy-file-browser/
├── docker-compose.yml          # Main orchestration file
├── api/
│   ├── Dockerfile              # Backend container definition
│   ├── .dockerignore          # Files to exclude from build
│   └── ...
├── web/
│   ├── Dockerfile              # Frontend container definition
│   ├── .dockerignore          # Files to exclude from build
│   └── ...
└── DOCKER.md                   # This file
```