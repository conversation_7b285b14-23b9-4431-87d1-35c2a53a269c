# Agrizy File Browser

A modern file browser application with role-based access control (RBAC), built with Vue.js frontend and Node.js/TypeScript backend.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- Port 3000 available for the web interface
- Internet connection for initial setup

### Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd agrizy-file-browser

# Run the automated setup script
./setup.sh
```

The setup script will:
- Check Docker availability
- Build and start all services (MySQL, API, Web)
- Automatically seed the database with admin user and roles
- Wait for services to be ready
- Display access information

### Manual Setup

If you prefer manual control:

```bash
# Start the services
docker-compose up -d --build

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### First-Time Access

After setup, access the application at **http://localhost:3000**

**Admin Access:**
- **Google OAuth**: `<EMAIL>` (automatic admin role)
- **Emergency Access**: `<EMAIL>` (local admin user)

**Default File Source**: Local Files (`/app/data` directory)

## 📋 Prerequisites

- Docker and Docker Compose
- Git
- Port 3000 available

## 🏗️ Architecture

### Services
- **Web Frontend**: Vue.js + TypeScript + Vite (Port 3000 - exposed)
- **API Backend**: Node.js + TypeScript + Fastify (Port 3030 - internal)
- **Database**: MySQL 8.0 (Port 3306 - internal)

### Key Features
- Role-based access control (RBAC)
- Multiple file provider support (Local Files, S3, etc.)
- File browsing, search, and preview
- User management and permissions
- Secure authentication with JWT

## 🗄️ Database Setup

### Automatic Initialization
When starting fresh, MySQL will automatically:
1. Create the database schema from `api/create-schema.sql`
2. Set up RBAC permissions from `api/init-rbac.sql`
3. Create default admin user and roles

### Default Admin User
After first startup, you can login with:
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Super Admin (full access)

⚠️ **Important**: Change the default admin password immediately after first login.

### Manual Database Seeding
If you need to reset or manually seed the database:

```bash
# Access MySQL container
docker-compose exec mysql mysql -u db_user -p agrizy_files_db

# Run seeding scripts manually
source /docker-entrypoint-initdb.d/01-schema.sql;
source /docker-entrypoint-initdb.d/02-rbac.sql;
```

## 🛠️ Development

### Local Development (without Docker)

#### Prerequisites
- Node.js 18+
- pnpm (recommended) or npm
- MySQL 8.0

#### Backend Setup
```bash
cd api
pnpm install

# Set up environment variables
cp .env.example .env
# Edit .env with your MySQL connection details

# Run database migrations
pnpm run migrate

# Start development server
pnpm run dev  # Runs on http://localhost:3030
```

#### Frontend Setup
```bash
cd web
pnpm install

# Start development server
pnpm run dev  # Runs on http://localhost:3000
```

### Environment Variables

#### Backend (.env)
```bash
NODE_ENV=development
PORT=3030
DB_HOST=localhost
DB_USER=db_user
DB_PASSWORD=jehREviconZygdan
DB_NAME=agrizy_files_db
DB_PORT=3306
DB_SSL=false
JWT_SECRET=your-secret-key-change-in-production
CORS_ORIGIN=http://localhost:3000
```

#### Frontend (.env)
```bash
VITE_API_BASE_URL=http://localhost:3030
VITE_APP_TITLE=Agrizy File Browser
```

## 🐳 Docker Commands

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Rebuild and start
docker-compose up -d --build

# Access MySQL database
docker-compose exec mysql mysql -u db_user -p agrizy_files_db

# View container status
docker-compose ps
```

## 📚 Documentation

- [Docker Setup Guide](DOCKER.md) - Detailed Docker configuration
- [Setup Instructions](SETUP.md) - Manual setup guide
- [API Documentation](api/openapi.yaml) - OpenAPI specification
- [Project Specifications](docs/specs/) - Detailed feature specifications

## 🔒 Security

### Default Security Measures
- JWT-based authentication
- Role-based access control (RBAC)
- CORS protection
- Input validation and sanitization
- Secure password hashing

### Production Considerations
- Change default admin credentials
- Update JWT_SECRET to a strong, random value
- Use environment-specific database credentials
- Enable HTTPS in production
- Regular security updates

## 🧪 Testing

```bash
# Run backend tests
cd api && pnpm test

# Run frontend tests
cd web && pnpm test

# Run E2E tests
pnpm run test:e2e
```

## 🤝 Contributing

### Development Guidelines
- Keep components and services simple and reusable
- Avoid code duplication
- Maintain consistent layouts, fonts, and styles
- Follow TypeScript best practices
- Write tests for new features
- Update documentation when needed

### Code Style
- Use ESLint and Prettier for code formatting
- Follow Vue.js style guide for frontend
- Use conventional commits for git messages

## 📄 License

[Add your license information here]

## 🆘 Troubleshooting

### Common Issues

**Port conflicts:**
```bash
# Check what's using the ports
lsof -ti:3000 -ti:3030

# Kill conflicting processes
kill -9 $(lsof -ti:3000) 2>/dev/null || true
kill -9 $(lsof -ti:3030) 2>/dev/null || true
```

**Database connection issues:**
```bash
# Check MySQL container logs
docker-compose logs mysql

# Verify database is healthy
docker-compose ps
```

**Application not loading:**
1. Ensure all containers are running: `docker-compose ps`
2. Check application logs: `docker-compose logs -f`
3. Verify port 3000 is accessible: `curl http://localhost:3000`

For more troubleshooting tips, see [DOCKER.md](DOCKER.md).
