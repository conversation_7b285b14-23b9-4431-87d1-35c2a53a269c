// Script to clear recent sources from localStorage
// This will remove invalid entries that are causing issues

console.log('Clearing recent sources from localStorage...');

// Check current localStorage content
const currentPrefs = localStorage.getItem('fb_prefs');
console.log('Current preferences:', currentPrefs);

if (currentPrefs) {
  try {
    const prefs = JSON.parse(currentPrefs);
    console.log('Current recent sources:', prefs.recentSources);
    
    // Clear recent sources
    prefs.recentSources = [];
    
    // Save back to localStorage
    localStorage.setItem('fb_prefs', JSON.stringify(prefs));
    console.log('Recent sources cleared successfully!');
    
    // Verify the change
    const updatedPrefs = JSON.parse(localStorage.getItem('fb_prefs'));
    console.log('Updated preferences:', updatedPrefs);
  } catch (error) {
    console.error('Error parsing preferences:', error);
    // If parsing fails, reset the entire preferences
    const defaultPrefs = {
      foldersFirst: true,
      hideHidden: true,
      recentSources: []
    };
    localStorage.setItem('fb_prefs', JSON.stringify(defaultPrefs));
    console.log('Reset preferences to defaults');
  }
} else {
  console.log('No preferences found in localStorage');
}

console.log('Script completed. Please refresh the page to see changes.');