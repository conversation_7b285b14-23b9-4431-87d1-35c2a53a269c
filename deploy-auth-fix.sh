#!/bin/bash

# Deployment script for authentication fix
# Run this on the server to pull and build the latest changes

echo "=== Deploying Authentication Fix ==="
echo ""

# Navigate to project directory
cd /home/<USER>/apps/file-browser

# Pull latest changes
echo "1. Pulling latest changes from git..."
git pull origin main

# Build frontend
echo ""
echo "2. Building frontend..."
cd web
npm run build

echo ""
echo "3. Build complete!"
echo ""
echo "=== Authentication Fix Deployed ==="
echo ""
echo "The fix changes Login.vue to use Vue Router navigation (router.push)"
echo "instead of window.location.href to avoid race condition with localStorage."
echo ""
echo "Please test at: https://files.agrizy.in"