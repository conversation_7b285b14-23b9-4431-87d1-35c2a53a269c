-- My<PERSON><PERSON> Schema for Agrizy File Browser
-- Create database tables

CREATE TABLE IF NOT EXISTS `sources` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `kind` VARCHAR(50) NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `config` JSON
);

CREATE TABLE IF NOT EXISTS `permissions` (
  `id` VARCHAR(50) PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL,
  `description` TEXT,
  `category` VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS `roles` (
  `id` VARCHAR(50) PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL,
  `description` TEXT,
  `is_builtin` BOOLEAN DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS `role_permissions` (
  `role_id` VARCHAR(50),
  `permission_id` VARCHAR(50),
  PRIMARY KEY (`role_id`, `permission_id`),
  FOR<PERSON><PERSON><PERSON>EY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
  FOR<PERSON><PERSON><PERSON> KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS `users` (
  `sub` VARCHAR(255) PRIMARY KEY,
  `email` VARCHAR(255) UNIQUE NOT NULL,
  `name` VARCHAR(255),
  `picture` TEXT,
  `blocked` BOOLEAN DEFAULT FALSE,
  `active` BOOLEAN DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS `user_roles` (
  `user_sub` VARCHAR(255),
  `role_id` VARCHAR(50),
  PRIMARY KEY (`user_sub`, `role_id`),
  FOREIGN KEY (`user_sub`) REFERENCES `users`(`sub`) ON DELETE CASCADE,
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS `user_permissions` (
  `user_sub` VARCHAR(255),
  `source_id` INT,
  `path` VARCHAR(500),
  `permission_id` VARCHAR(50),
  PRIMARY KEY (`user_sub`, `source_id`, `path`(255), `permission_id`),
  FOREIGN KEY (`user_sub`) REFERENCES `users`(`sub`) ON DELETE CASCADE,
  FOREIGN KEY (`source_id`) REFERENCES `sources`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS `audit_logs` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `user_sub` VARCHAR(255),
  `action` VARCHAR(100) NOT NULL,
  `resource_type` VARCHAR(100),
  `resource_id` VARCHAR(255),
  `details` JSON,
  `ip_address` VARCHAR(45),
  `user_agent` TEXT,
  FOREIGN KEY (`user_sub`) REFERENCES `users`(`sub`) ON DELETE SET NULL,
  INDEX `idx_timestamp` (`timestamp`),
  INDEX `idx_user_sub` (`user_sub`),
  INDEX `idx_action` (`action`)
);

-- Create indexes for better performance (MySQL doesn't support IF NOT EXISTS for indexes, so we'll handle errors gracefully)
CREATE INDEX `idx_sources_kind` ON `sources`(`kind`);
CREATE INDEX `idx_users_email` ON `users`(`email`);
CREATE INDEX `idx_user_permissions_source_path` ON `user_permissions`(`source_id`, `path`);