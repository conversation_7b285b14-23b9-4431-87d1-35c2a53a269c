-- Core Database Seeding Script for Agrizy File Browser (without user_groups)
-- This script sets up the initial database with admin user, roles, permissions, and default source

-- =============================================================================
-- ROLES AND PERMISSIONS SETUP
-- =============================================================================

-- Insert basic permissions
INSERT IGNORE INTO `permissions` (`id`, `name`, `description`, `category`) VALUES
('admin.all', 'Full Admin Access', 'Complete administrative access to all features', 'admin'),
('sources.read', 'Read Sources', 'View file sources and browse files', 'sources'),
('sources.write', 'Write Sources', 'Create, update, and delete file sources', 'sources'),
('sources.admin', 'Admin Sources', 'Full administrative access to sources', 'sources'),
('files.read', 'Read Files', 'View and download files', 'files'),
('files.write', 'Write Files', 'Upload, modify, and delete files', 'files'),
('files.admin', 'Admin Files', 'Full administrative access to files', 'files'),
('users.read', 'Read Users', 'View user information', 'users'),
('users.write', 'Write Users', 'Create and modify users', 'users'),
('users.admin', 'Admin Users', 'Full administrative access to users', 'users'),
('audit.read', 'Read Audit Logs', 'View audit logs and system activity', 'audit'),
('system.admin', 'System Admin', 'System-level administrative access', 'system');

-- Insert basic roles
INSERT IGNORE INTO `roles` (`id`, `name`, `description`, `is_builtin`) VALUES
('admin', 'Administrator', 'Full system administrator with all permissions', TRUE),
('manager', 'Manager', 'Manager with read/write access to most features', TRUE),
('editor', 'Editor', 'Editor with file read/write access', TRUE),
('user', 'User', 'Standard user with basic file access', TRUE),
('viewer', 'Viewer', 'Read-only access to files and sources', TRUE);

-- Assign permissions to admin role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('admin', 'admin.all'),
('admin', 'sources.admin'),
('admin', 'files.admin'),
('admin', 'users.admin'),
('admin', 'audit.read'),
('admin', 'system.admin');

-- Assign permissions to manager role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('manager', 'sources.read'),
('manager', 'sources.write'),
('manager', 'files.read'),
('manager', 'files.write'),
('manager', 'users.read'),
('manager', 'audit.read');

-- Assign permissions to editor role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('editor', 'sources.read'),
('editor', 'files.read'),
('editor', 'files.write');

-- Assign permissions to user role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('user', 'sources.read'),
('user', 'files.read'),
('user', 'files.write');

-- Assign permissions to viewer role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('viewer', 'sources.read'),
('viewer', 'files.read');

-- =============================================================================
-- DEFAULT ADMIN USER SETUP
-- =============================================================================

-- Create default admin user (for emergency access)
INSERT IGNORE INTO `users` (`sub`, `email`, `name`, `blocked`, `active`) VALUES
('admin-local-001', '<EMAIL>', 'System Administrator', FALSE, TRUE);

-- Assign admin role to default admin user
INSERT IGNORE INTO `user_roles` (`user_sub`, `role_id`) VALUES
('admin-local-001', 'admin');

-- Ensure <EMAIL> has admin role when they login
-- (This will be created automatically on first login, we just ensure the role is assigned)
INSERT IGNORE INTO `user_roles` (`user_sub`, `role_id`) 
SELECT sub, 'admin' FROM users WHERE email = '<EMAIL>';

-- =============================================================================
-- DEFAULT FILE SOURCES SETUP
-- =============================================================================

-- Create a default "Local Files" source for file browsing
INSERT IGNORE INTO `sources` (`id`, `kind`, `name`, `config`) VALUES
(1, 'local', 'Local Files', JSON_OBJECT(
    'path', '/app/data',
    'readonly', false,
    'description', 'Default local file storage'
));

-- Grant admin user full access to the default source
INSERT IGNORE INTO `user_permissions` (`user_sub`, `source_id`, `path`, `permission_id`) VALUES
('admin-local-001', 1, '/', 'sources.admin'),
('admin-local-001', 1, '/', 'files.admin');

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify roles and permissions setup
SELECT 'Roles Created' as status, COUNT(*) as count FROM roles WHERE is_builtin = TRUE;
SELECT 'Permissions Created' as status, COUNT(*) as count FROM permissions;
SELECT 'Role Permissions Assigned' as status, COUNT(*) as count FROM role_permissions;

-- Verify admin user creation
SELECT 'Admin Users' as status, email, active, 
       (SELECT GROUP_CONCAT(role_id) FROM user_roles WHERE user_sub = users.sub) as roles
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Verify default source
SELECT 'Default Source' as status, id, kind, name FROM sources WHERE id = 1;

-- Final status
SELECT 'Database Seeding Complete!' as status;