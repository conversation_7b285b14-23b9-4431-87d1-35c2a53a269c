-- Database Seeding Script for Agrizy File Browser
-- This script sets up the initial database with admin user, roles, permissions, and default source
-- Run this script after the database schema has been created

-- =============================================================================
-- ROLES AND PERMISSIONS SETUP
-- =============================================================================

-- Insert basic permissions
INSERT IGNORE INTO `permissions` (`id`, `name`, `description`, `category`) VALUES
('admin.all', 'Full Admin Access', 'Complete administrative access to all features', 'admin'),
('sources.read', 'Read Sources', 'View file sources and browse files', 'sources'),
('sources.write', 'Write Sources', 'Create, update, and delete file sources', 'sources'),
('sources.admin', 'Admin Sources', 'Full administrative access to sources', 'sources'),
('files.read', 'Read Files', 'View and download files', 'files'),
('files.write', 'Write Files', 'Upload, modify, and delete files', 'files'),
('files.admin', 'Admin Files', 'Full administrative access to files', 'files'),
('users.read', 'Read Users', 'View user information', 'users'),
('users.write', 'Write Users', 'Create and modify users', 'users'),
('users.admin', 'Admin Users', 'Full administrative access to users', 'users'),
('audit.read', 'Read Audit Logs', 'View audit logs and system activity', 'audit'),
('system.admin', 'System Admin', 'System-level administrative access', 'system');

-- Insert basic roles
INSERT IGNORE INTO `roles` (`id`, `name`, `description`, `is_builtin`) VALUES
('admin', 'Administrator', 'Full system administrator with all permissions', TRUE),
('manager', 'Manager', 'Manager with read/write access to most features', TRUE),
('editor', 'Editor', 'Editor with file read/write access', TRUE),
('user', 'User', 'Standard user with basic file access', TRUE),
('viewer', 'Viewer', 'Read-only access to files and sources', TRUE);

-- Assign permissions to admin role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('admin', 'admin.all'),
('admin', 'sources.admin'),
('admin', 'files.admin'),
('admin', 'users.admin'),
('admin', 'audit.read'),
('admin', 'system.admin');

-- Assign permissions to manager role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('manager', 'sources.read'),
('manager', 'sources.write'),
('manager', 'files.read'),
('manager', 'files.write'),
('manager', 'users.read'),
('manager', 'audit.read');

-- Assign permissions to editor role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('editor', 'sources.read'),
('editor', 'files.read'),
('editor', 'files.write');

-- Assign permissions to user role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('user', 'sources.read'),
('user', 'files.read'),
('user', 'files.write');

-- Assign permissions to viewer role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('viewer', 'sources.read'),
('viewer', 'files.read');

-- =============================================================================
-- DEFAULT ADMIN USER SETUP
-- =============================================================================

-- Create default admin user
-- Note: This creates a local admin user that can be used for initial setup
-- In production, change the credentials immediately after first login
INSERT IGNORE INTO `users` (`sub`, `email`, `name`, `blocked`, `active`) VALUES
('admin-local-001', '<EMAIL>', 'System Administrator', FALSE, TRUE);

-- Assign admin role to default admin user
INSERT IGNORE INTO `user_roles` (`user_sub`, `role_id`) VALUES
('admin-local-001', 'admin');

-- =============================================================================
-- DEFAULT FILE SOURCES SETUP
-- =============================================================================

-- Create a default "Local Files" source for file browsing
INSERT IGNORE INTO `sources` (`id`, `kind`, `name`, `config`) VALUES
(1, 'local', 'Local Files', JSON_OBJECT(
    'path', '/app/data',
    'readonly', false,
    'description', 'Default local file storage'
));

-- Grant admin user full access to the default source
INSERT IGNORE INTO `user_permissions` (`user_sub`, `source_id`, `path`, `permission_id`) VALUES
('admin-local-001', 1, '/', 'sources.admin'),
('admin-local-001', 1, '/', 'files.admin');

-- =============================================================================
-- SAMPLE DATA (OPTIONAL)
-- =============================================================================

-- Create sample user groups
INSERT IGNORE INTO `user_groups` (`id`, `name`, `description`) VALUES
(1, 'Administrators', 'System administrators with full access'),
(2, 'Managers', 'Department managers with elevated permissions'),
(3, 'Staff', 'Regular staff members with standard access');

-- Add admin user to administrators group
INSERT IGNORE INTO `user_group_members` (`user_group_id`, `user_sub`) VALUES
(1, 'admin-local-001');

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify admin user creation
SELECT 
    'Admin User Setup' as verification_step,
    u.sub,
    u.email,
    u.name,
    u.active,
    u.blocked,
    GROUP_CONCAT(ur.role_id) as roles
FROM users u
LEFT JOIN user_roles ur ON u.sub = ur.user_sub
WHERE u.sub = 'admin-local-001'
GROUP BY u.sub, u.email, u.name, u.active, u.blocked;

-- Verify default source creation
SELECT 
    'Default Source Setup' as verification_step,
    s.id,
    s.kind,
    s.name,
    s.config
FROM sources s
WHERE s.id = 1;

-- Verify admin permissions on default source
SELECT 
    'Admin Source Permissions' as verification_step,
    up.user_sub,
    up.source_id,
    up.path,
    up.permission_id,
    p.name as permission_name
FROM user_permissions up
JOIN permissions p ON up.permission_id = p.id
WHERE up.user_sub = 'admin-local-001' AND up.source_id = 1;

-- Verify role permissions
SELECT 
    'Role Permissions Summary' as verification_step,
    r.id as role_id,
    r.name as role_name,
    COUNT(rp.permission_id) as permission_count,
    GROUP_CONCAT(rp.permission_id ORDER BY rp.permission_id) as permissions
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
WHERE r.is_builtin = TRUE
GROUP BY r.id, r.name
ORDER BY r.id;

-- =============================================================================
-- SETUP COMPLETION MESSAGE
-- =============================================================================

SELECT 
    'Database Seeding Complete!' as status,
    'Default admin user created with email: <EMAIL>' as admin_info,
    'Use Google <NAME_EMAIL> for admin access' as auth_info,
    'Default Local Files source created at /app/data' as source_info;

-- =============================================================================
-- IMPORTANT SECURITY NOTES
-- =============================================================================

/*
SECURITY CHECKLIST AFTER RUNNING THIS SCRIPT:

1. AUTHENTICATION:
   - The system uses Google OAuth for authentication
   - Admin access is <NAME_EMAIL> automatically
   - Local admin user (<EMAIL>) is for emergency access only

2. DEFAULT CREDENTIALS:
   - No default passwords are set (OAuth-based authentication)
   - Admin role is automatically <NAME_EMAIL>

3. PRODUCTION SETUP:
   - Update JWT_SECRET environment variable
   - Configure proper CORS_ORIGIN
   - Set up proper SSL/TLS certificates
   - Review and update allowed domains in auth.ts
   - Monitor audit logs regularly

4. FILE PERMISSIONS:
   - Default source points to /app/data directory
   - Ensure proper file system permissions on the host
   - Consider setting up additional sources as needed

5. DATABASE SECURITY:
   - Change default database passwords
   - Restrict database access to application only
   - Enable database SSL if required
   - Set up regular database backups

6. MONITORING:
   - All authentication attempts are logged in audit_logs table
   - Monitor failed login attempts
   - Review user role assignments regularly
*/