-- Initialize RBAC system with basic roles and permissions

-- Insert basic permissions
INSERT IGNORE INTO `permissions` (`id`, `name`, `description`, `category`) VALUES
('admin.all', 'Full Admin Access', 'Complete administrative access to all features', 'admin'),
('sources.read', 'Read Sources', 'View file sources and browse files', 'sources'),
('sources.write', 'Write Sources', 'Create, update, and delete file sources', 'sources'),
('sources.admin', 'Admin Sources', 'Full administrative access to sources', 'sources'),
('files.read', 'Read Files', 'View and download files', 'files'),
('files.write', 'Write Files', 'Upload, modify, and delete files', 'files'),
('files.admin', 'Admin Files', 'Full administrative access to files', 'files'),
('users.read', 'Read Users', 'View user information', 'users'),
('users.write', 'Write Users', 'Create and modify users', 'users'),
('users.admin', 'Admin Users', 'Full administrative access to users', 'users'),
('audit.read', 'Read Audit Logs', 'View audit logs and system activity', 'audit'),
('system.admin', 'System Admin', 'System-level administrative access', 'system');

-- Insert basic roles
INSERT IGNORE INTO `roles` (`id`, `name`, `description`, `is_builtin`) VALUES
('admin', 'Administrator', 'Full system administrator with all permissions', TRUE),
('manager', 'Manager', 'Manager with read/write access to most features', TRUE),
('user', 'User', 'Standard user with basic file access', TRUE),
('viewer', 'Viewer', 'Read-only access to files and sources', TRUE);

-- Assign permissions to admin role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('admin', 'admin.all'),
('admin', 'sources.admin'),
('admin', 'files.admin'),
('admin', 'users.admin'),
('admin', 'audit.read'),
('admin', 'system.admin');

-- Assign permissions to manager role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('manager', 'sources.read'),
('manager', 'sources.write'),
('manager', 'files.read'),
('manager', 'files.write'),
('manager', 'users.read'),
('manager', 'audit.read');

-- Assign permissions to user role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('user', 'sources.read'),
('user', 'files.read'),
('user', 'files.write');

-- Assign permissions to viewer role
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
('viewer', 'sources.read'),
('viewer', 'files.read');

-- Create default admin user
-- Note: In production, change the password immediately after first login
INSERT IGNORE INTO `users` (`sub`, `email`, `name`, `blocked`, `active`) VALUES
('admin-local-001', '<EMAIL>', 'System Administrator', FALSE, TRUE);

-- Assign admin role to default admin user
INSERT IGNORE INTO `user_roles` (`user_sub`, `role_id`) VALUES
('admin-local-001', 'admin');

-- Create a default "Local Files" source for file browsing
INSERT IGNORE INTO `sources` (`id`, `kind`, `name`, `config`) VALUES
(1, 'local', 'Local Files', JSON_OBJECT('path', '/app/data', 'readonly', false));

-- Grant admin user full access to the default source
INSERT IGNORE INTO `user_permissions` (`user_sub`, `source_id`, `path`, `permission_id`) VALUES
('admin-local-001', 1, '/', 'sources.admin'),
('admin-local-001', 1, '/', 'files.admin');

-- Verify the setup
SELECT 
    'Default Admin User Created' as status,
    u.email,
    u.sub,
    u.name,
    ur.role_id,
    r.name as role_name
FROM users u
LEFT JOIN user_roles ur ON u.sub = ur.user_sub
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.sub = 'admin-local-001';

SELECT 
    'Default Source Created' as status,
    s.id,
    s.kind,
    s.name,
    s.config
FROM sources s
WHERE s.id = 1;