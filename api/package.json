{"name": "agrizy-file-browser-api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc -p tsconfig.json", "start": "node dist/server.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.620.0", "@aws-sdk/s3-request-presigner": "^3.620.0", "@fastify/cors": "^8.4.2", "archiver": "^6.0.2", "dotenv": "^16.4.5", "fastify": "^4.28.1", "google-auth-library": "^9.14.1", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "mysql2": "^3.6.5", "pino": "^9.0.0", "unzipper": "^0.10.14", "zod": "^3.23.8"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/mime-types": "^3.0.1", "@types/node": "^20.11.0", "@types/unzipper": "^0.10.11", "tsx": "^4.7.0", "typescript": "^5.4.0"}}