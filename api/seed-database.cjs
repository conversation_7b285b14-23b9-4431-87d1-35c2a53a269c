const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// Database configuration from environment variables
const config = {
  host: process.env.DB_HOST || 'agrizy-mysql',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root_password',
  database: process.env.DB_NAME || 'agrizy_file_browser',
  multipleStatements: true,
  ssl: false
};

async function waitForDatabase(maxRetries = 30, delay = 2000) {
  console.log('🔄 Waiting for MySQL to be ready...');
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password
      });
      
      await connection.execute('SELECT 1');
      await connection.end();
      console.log('✅ MySQL is ready!');
      return true;
    } catch (error) {
      console.log(`⏳ Attempt ${i + 1}/${maxRetries}: MySQL not ready yet...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error('❌ MySQL failed to become ready within timeout period');
}

async function createDatabase() {
  console.log('🗄️  Creating database if not exists...');
  
  const connection = await mysql.createConnection({
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password
  });
  
  await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${config.database}\``);
  await connection.end();
  
  console.log(`✅ Database '${config.database}' is ready!`);
}

async function checkDataExists() {
  console.log('🔍 Checking if data already exists...');
  
  try {
    const connection = await mysql.createConnection(config);
    
    // Check if admin user exists
    const [adminUsers] = await connection.execute(
      "SELECT COUNT(*) as count FROM users WHERE email = '<EMAIL>'"
    );
    
    // Check if Local Files source exists
    const [sources] = await connection.execute(
      "SELECT COUNT(*) as count FROM sources WHERE name = 'Local Files'"
    );
    
    await connection.end();
    
    const hasAdminUser = adminUsers[0].count > 0;
    const hasLocalSource = sources[0].count > 0;
    
    console.log(`📊 Data check results:`);
    console.log(`   - Admin user exists: ${hasAdminUser}`);
    console.log(`   - Local Files source exists: ${hasLocalSource}`);
    
    return hasAdminUser && hasLocalSource;
    
  } catch (error) {
    // If tables don't exist yet, that's fine
    if (error.code === 'ER_NO_SUCH_TABLE') {
      console.log('📋 Tables not found, proceeding with full setup...');
      return false;
    }
    throw error;
  }
}

async function executeSqlFile(filePath, description, skipIfDataExists = false) {
  console.log(`📄 ${description}...`);
  
  try {
    const sqlContent = await fs.readFile(filePath, 'utf8');
    
    if (!sqlContent.trim()) {
      console.log(`⚠️  ${filePath} is empty, skipping...`);
      return;
    }
    
    // Check if we should skip seeding files when data exists
    if (skipIfDataExists && filePath.includes('seed-database.sql')) {
      const dataExists = await checkDataExists();
      if (dataExists) {
        console.log(`⏭️  Data already exists, skipping ${description}...`);
        return;
      }
    }
    
    const connection = await mysql.createConnection(config);
    
    // Split SQL content by semicolons and execute each statement
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
        } catch (error) {
          // Ignore duplicate entry and duplicate key errors for idempotent operations
          if (error.code === 'ER_DUP_ENTRY' || error.code === 'ER_DUP_KEYNAME') {
            console.log(`⚠️  Duplicate ${error.code === 'ER_DUP_ENTRY' ? 'entry' : 'key'} ignored: ${error.message}`);
            continue;
          }
          throw error;
        }
      }
    }
    
    await connection.end();
    console.log(`✅ ${description} completed successfully!`);
    
  } catch (error) {
    console.error(`❌ Error in ${description}:`, error.message);
    throw error;
  }
}

async function verifySetup() {
  console.log('🔍 Verifying database setup...');
  
  try {
    const connection = await mysql.createConnection(config);
    
    // Check if tables exist
    const [tables] = await connection.execute(
      "SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = ?",
      [config.database]
    );
    
    console.log(`📊 Found ${tables.length} tables:`, tables.map(t => t.TABLE_NAME).join(', '));
    
    // Check if admin user exists
    const [users] = await connection.execute(
      "SELECT email, name FROM users WHERE email = '<EMAIL>'"
    );
    
    if (users.length > 0) {
      console.log('👤 Admin user found:', users[0].email);
    } else {
      console.log('⚠️  Admin user not found');
    }
    
    // Check sources
    const [sources] = await connection.execute("SELECT name FROM sources");
    console.log(`📁 Found ${sources.length} sources:`, sources.map(s => s.name).join(', '));
    
    await connection.end();
    console.log('✅ Database verification completed!');
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting database seeding process...');
    console.log('📋 Configuration:', {
      host: config.host,
      port: config.port,
      user: config.user,
      database: config.database
    });
    
    // Wait for MySQL to be ready
    await waitForDatabase();
    
    // Create database
    await createDatabase();
    
    // Execute SQL files in order
    const sqlFiles = [
      { path: '/app/create-schema.sql', description: 'Creating database schema', skipIfExists: false },
      { path: '/app/init-rbac.sql', description: 'Initializing RBAC system', skipIfExists: false },
      { path: '/app/seed-database.sql', description: 'Seeding initial data', skipIfExists: true }
    ];
    
    for (const sqlFile of sqlFiles) {
      if (await fs.access(sqlFile.path).then(() => true).catch(() => false)) {
        await executeSqlFile(sqlFile.path, sqlFile.description, sqlFile.skipIfExists);
      } else {
        console.log(`⚠️  ${sqlFile.path} not found, skipping...`);
      }
    }
    
    // Verify setup
    await verifySetup();
    
    console.log('🎉 Database seeding completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('💥 Database seeding failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = { main, waitForDatabase, createDatabase, executeSqlFile, verifySetup };