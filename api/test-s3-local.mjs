import jwt from 'jsonwebtoken';
import 'dotenv/config';

// Generate admin token
const token = jwt.sign(
  { sub: '<EMAIL>', email: '<EMAIL>', name: 'Admin' },
  'your-secret-key',
  { expiresIn: '1h' }
);

console.log('Testing S3 source locally...\n');

// Test the S3 source
const response = await fetch('http://localhost:3030/v1/sources/59/test', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({})
});

const result = await response.json();
console.log('Status:', response.status);
console.log('Response:', JSON.stringify(result, null, 2));

if (response.ok) {
  console.log('\n✅ S3 test successful!');
} else {
  console.log('\n❌ S3 test failed:', result.error?.message || 'Unknown error');
}