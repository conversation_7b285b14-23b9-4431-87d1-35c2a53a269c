openapi: 3.0.3
info:
  title: Agrizy File Browser API (v2)
  version: 0.1.0
servers:
  - url: http://localhost:4000
paths:
  /v1/health:
    get:
      summary: Health check
      responses:
        '200':
          description: OK
  /v1/files:
    get:
      summary: List files
      parameters:
        - in: query
          name: provider
          schema: { type: string }
        - in: query
          name: path
          schema: { type: string }
        - in: query
          name: page
          schema: { type: integer }
        - in: query
          name: size
          schema: { type: integer }
      responses:
        '200':
          description: List of files
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        name: { type: string }
                        path: { type: string }
                        kind: { type: string, enum: [file, dir] }
                  page: { type: integer }
                  size: { type: integer }
                  total: { type: integer }
    post:
      summary: Create directory
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                type: { type: string, enum: [dir] }
      responses:
        '200': { description: OK }
    delete:
      summary: Delete path
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
      responses:
        '200': { description: OK }
  /v1/files/stat:
    get:
      summary: Stat a path
      parameters:
        - in: query
          name: provider
          schema: { type: string }
        - in: query
          name: path
          schema: { type: string }
      responses:
        '200':
          description: Info
  /v1/files/move:
    put:
      summary: Move a path
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                src: { type: string }
                dst: { type: string }
      responses:
        '200': { description: OK }
  /v1/files/copy:
    put:
      summary: Copy a path
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                src: { type: string }
                dst: { type: string }
      responses:
        '200': { description: OK }
  /v1/files:
    post:
      summary: Create directory
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                type: { type: string, enum: [dir] }
      responses:
        '200': { description: OK }
    delete:
      summary: Delete path
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
      responses:
        '200': { description: OK }
  /v1/files/zip:
    post:
      summary: Zip multiple paths
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                paths: { type: array, items: { type: string } }
                dst: { type: string }
      responses:
        '200': { description: OK }
  /v1/files/unzip:
    post:
      summary: Unzip archive
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                src: { type: string }
                dst: { type: string }
      responses:
        '200': { description: OK }
  /v1/uploads/presign:
    post:
      summary: Presign upload
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                ttlSeconds: { type: integer }
      responses:
        '200': { description: OK }
  /v1/downloads/presign:
    post:
      summary: Presign download
  /v1/files/download:
    get:
      summary: Download a file (FS, supports Range)
      parameters:
        - in: query
          name: provider
          schema: { type: string }
        - in: query
          name: path
          schema: { type: string }
      responses:
        '200': { description: OK }
        '206': { description: Partial content }
  /v1/files/upload:
    post:
      summary: Upload a file (FS stream)
      parameters:
        - in: query
          name: provider
          schema: { type: string }
        - in: query
          name: path
          schema: { type: string }
        - in: query
          name: overwrite
          schema: { type: boolean }
      requestBody:
        required: true
        content:
          application/octet-stream: {}
      responses:
        '200': { description: OK }
  /v1/uploads/multipart/init:
    post:
      summary: Init S3 multipart upload
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                contentType: { type: string }
      responses:
        '200': { description: OK }
  /v1/uploads/multipart/presignPart:
    post:
      summary: Presign S3 upload part
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                uploadId: { type: string }
                partNumber: { type: integer }
                ttlSeconds: { type: integer }
      responses:
        '200': { description: OK }
  /v1/uploads/multipart/complete:
    post:
      summary: Complete S3 multipart upload
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                uploadId: { type: string }
                parts:
                  type: array
                  items:
                    type: object
                    properties:
                      ETag: { type: string }
                      PartNumber: { type: integer }
      responses:
        '200': { description: OK }
  /v1/uploads/multipart/abort:
    post:
      summary: Abort S3 multipart upload
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                uploadId: { type: string }
      responses:
        '200': { description: OK }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                provider: { type: string }
                path: { type: string }
                ttlSeconds: { type: integer }
      responses:
        '200': { description: OK }
