# Use Node.js 18 Alpine as base image
FROM node:18-alpine

# Install mysql client for database operations
RUN apk add --no-cache mysql-client

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Make seed script executable
RUN chmod +x /app/seed-db.sh

# No local database directory needed - using MySQL

# Build the TypeScript application
RUN pnpm run build

# Expose the port
EXPOSE 3030

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3030

# Start the application
CMD ["pnpm", "start"]