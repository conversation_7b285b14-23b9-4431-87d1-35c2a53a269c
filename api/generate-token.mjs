import crypto from 'crypto';

function signToken(user, secret) {
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url');
  const payload = Buffer.from(JSON.stringify({ user, iat: Math.floor(Date.now() / 1000) })).toString('base64url');
  const h = crypto.createHmac('sha256', secret).update(`${header}.${payload}`).digest('base64url');
  return `${header}.${payload}.${h}`;
}

const user = { sub: 'admin-local-001', email: '<EMAIL>', name: 'Admin' };
const secret = 'dev-secret-change-me';
const token = signToken(user, secret);
console.log('Token:', token);