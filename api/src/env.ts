import 'dotenv/config'

export type S3Config = {
  bucket?: string
  region?: string
  accessKeyId?: string
  secretAccessKey?: string
  endpoint?: string
  forcePathStyle?: boolean
  rootPrefix?: string
  bucketUrl?: string
  ipmBucketUrl?: string
}

export type AppEnv = {
  fsRoot: string
  s3: S3Config
  google?: {
    clientId?: string
    clientSecret?: string
  }
  appSecret: string
}

export function loadEnv(): AppEnv {
  const s3: S3Config = {
    bucket: process.env.S3_BUCKET || process.env.AWS_S3_BUCKET_NAME,
    region: process.env.S3_REGION || process.env.AWS_S3_REGION_NAME,
    accessKeyId: process.env.S3_ACCESS_KEY_ID || process.env.AWS_S3_ACCESS_KEY_ID,
    secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || process.env.AWS_S3_SECRET_ACCESS_KEY,
    endpoint: process.env.S3_ENDPOINT,
    forcePathStyle: process.env.S3_FORCE_PATH_STYLE === 'true',
    rootPrefix: process.env.S3_ROOT_PREFIX || '',
    bucketUrl: process.env.AWS_S3_BUCKET_URL,
    ipmBucketUrl: process.env.AWS_S3_IPM_BUCKET_URL,
  }

  return {
    fsRoot: process.env.FS_ROOT || process.cwd(),
    s3,
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    },
    appSecret: process.env.APP_SECRET || 'dev-secret-change-me',
  }
}
