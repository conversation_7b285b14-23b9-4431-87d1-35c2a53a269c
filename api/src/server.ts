import Fastify from 'fastify'
import cors from '@fastify/cors'
import { registerFileRoutes } from './routes/files.js'
import { registerPresignRoutes } from './routes/presign.js'
import { registerStreamRoutes } from './routes/stream.js'
import { registerMultipartRoutes } from './routes/multipart.js'
import { registerSearchRoutes } from './routes/search.js'
import { registerPreviewRoutes } from './routes/preview.js'
import { registerAuthRoutes } from './routes/auth.js'
import { loadEnv } from './env.js'
import { readAuthBearer, verifyToken } from './utils/auth.js'
import { registerSourceRoutes } from './routes/sources.js'
import { registerAdminRoutes } from './routes/admin.js'
import { registerDefaultProviders } from './providers/bootstrap.js'
import { db } from './db.js'
import { installErrorHandler } from './utils/errors.js'

const app = Fastify({ logger: true })

await app.register(cors, { origin: true })

app.get('/v1/health', async () => ({ status: 'ok' }))

// Register providers from env
registerDefaultProviders()

await registerAuthRoutes(app)

// Simple auth gate for all /v1/* except /v1/health and /v1/auth/*
const env = loadEnv()
app.addHook('preHandler', async (req, reply) => {
  const url = req.url || ''
  if (url.startsWith('/v1/auth') || url === '/v1/health') return
  let token = readAuthBearer(req as any)
  console.log('Auth middleware - URL:', url, 'Token present:', !!token)
  // Allow token via query for download links
  if (!token) {
    const q = (req.query || {}) as any
    if (q && typeof q.token === 'string') token = q.token
  }
  const user = token ? verifyToken(token, env.appSecret) : null
  console.log('Auth middleware - User verified:', !!user, 'User sub:', user?.sub)
  if (!user || !user.sub) return reply.code(401).send({ error: { code: 'UNAUTHORIZED', message: 'login required' } })
  try {
    const row: any = await db.prepare('SELECT blocked, active FROM users WHERE sub=?').get(user.sub)
    console.log('Auth middleware - User DB record:', row)
    if (row && row.blocked) return reply.code(403).send({ error: { code: 'BLOCKED', message: 'account blocked' } })
    if (row && !row.active) return reply.code(403).send({ error: { code: 'INACTIVE', message: 'account not activated. Please contact an administrator.' } })
  } catch (error) {
    console.log('Auth middleware - DB query error:', error)
  }
  ;(req as any).user = user
  console.log('Auth middleware - User set on request:', (req as any).user?.sub)
})

await registerFileRoutes(app)
await registerSourceRoutes(app)
await registerAdminRoutes(app)
await registerPresignRoutes(app)
await registerStreamRoutes(app)
await registerMultipartRoutes(app)
await registerSearchRoutes(app)
await registerPreviewRoutes(app)

// Unified error responses
installErrorHandler(app)

const port = Number(process.env.PORT) || 3030
const host = process.env.HOST || '0.0.0.0'

app
  .listen({ port, host })
  .catch((err) => {
    app.log.error(err)
    process.exit(1)
  })
