#!/usr/bin/env node

import { readFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { db } from './db.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

async function initializeRBAC() {
  try {
    console.log('🚀 Initializing RBAC system...')
    
    // Read the SQL file
    const sqlPath = join(__dirname, '..', 'init-rbac.sql')
    const sqlContent = readFileSync(sqlPath, 'utf8')
    
    // Split SQL statements by semicolon and filter out empty statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`)
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement.toLowerCase().startsWith('select')) {
        // For SELECT statements, show the results
        console.log(`\n🔍 Executing query ${i + 1}:`);
        const result = await db.prepare(statement).all()
        console.table(result)
      } else {
        // For other statements, just execute
        console.log(`✅ Executing statement ${i + 1}: ${statement.substring(0, 50)}...`)
        await db.prepare(statement).run()
      }
    }
    
    console.log('\n🎉 RBAC initialization completed successfully!')
    console.log('\n📊 Current permissions:')
    const permissions = await db.prepare('SELECT * FROM permissions').all()
    console.table(permissions)
    
    console.log('\n📊 Current roles:')
    const roles = await db.prepare('SELECT * FROM roles').all()
    console.table(roles)
    
    console.log('\n📊 Current role permissions:')
    const rolePermissions = await db.prepare(`
      SELECT 
        rp.role_id,
        rp.permission_id,
        r.name as role_name,
        p.name as permission_name,
        p.description as permission_description
      FROM role_permissions rp
      JOIN roles r ON rp.role_id = r.id
      JOIN permissions p ON rp.permission_id = p.id
    `).all()
    console.table(rolePermissions)
    
    // Log permissions, roles, and role permissions for debugging
    console.log('\n=== PERMISSIONS TABLE ===')
    console.table(permissions)
    
    console.log('\n=== ROLES TABLE ===')
    console.table(roles)
    
    console.log('\n=== ROLE_PERMISSIONS TABLE ===')
    const rawRolePermissions = await db.prepare('SELECT * FROM role_permissions').all()
    console.table(rawRolePermissions)
    
    console.log('\n=== ADMIN USER PERMISSIONS FROM ENDPOINT ===')
    const adminUserRoles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub = ?').all('101133219535942343844')
    console.log('Admin user roles:', adminUserRoles.map(r => r.role_id))
    
    const adminPermissions = new Set()
    for (const roleId of adminUserRoles.map(r => r.role_id)) {
      const rolePerms = await db.prepare('SELECT permission_id FROM role_permissions WHERE role_id = ?').all(roleId)
      rolePerms.forEach(rp => adminPermissions.add(rp.permission_id))
    }
    console.log('Admin user permissions from endpoint logic:', Array.from(adminPermissions))
    
    console.log('\n📊 Current user roles:')
    
    // Show final user roles
    const userRoles = await db.prepare(`
      SELECT 
        u.email,
        u.sub,
        ur.role_id,
        r.name as role_name,
        r.description as role_description
      FROM users u
      LEFT JOIN user_roles ur ON u.sub = ur.user_sub
      LEFT JOIN roles r ON ur.role_id = r.id
      WHERE u.email = '<EMAIL>'
    `).all()
    
    console.table(userRoles)
    
  } catch (error) {
    console.error('❌ Error initializing RBAC:', error)
    process.exit(1)
  }
}

// Run the initialization
initializeRBAC().then(() => {
  console.log('\n✨ RBAC initialization script completed')
  process.exit(0)
}).catch(error => {
  console.error('💥 Fatal error:', error)
  process.exit(1)
})