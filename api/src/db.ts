import 'dotenv/config'
import mysql from 'mysql2/promise'
import fs from 'fs'
import path from 'path'

const dataDir = path.resolve(process.cwd(), 'data')
if (!fs.existsSync(dataDir)) fs.mkdirSync(dataDir, { recursive: true })

type Row = Record<string, any>

// MySQL connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'mysql',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root_password',
  database: process.env.DB_NAME || 'agrizy_files_db',
  port: parseInt(process.env.DB_PORT || '3306'),
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : undefined,
  connectTimeout: 60000,
  acquireTimeout: 60000
}

// Create connection pool
export const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
})

// Database wrapper to maintain compatibility with existing code
export const db = {
  async exec(sql: string) {
    const connection = await pool.getConnection()
    try {
      await connection.execute(sql)
    } finally {
      connection.release()
    }
  },
  
  prepare(sql: string) {
    return {
      async run(...args: any[]) {
        const connection = await pool.getConnection()
        try {
          // Filter out undefined values and replace with null for MySQL
          const filteredArgs = args.map(arg => arg === undefined ? null : arg)
          const [result] = await connection.execute(sql, filteredArgs)
          const mysqlResult = result as any
          return {
            changes: mysqlResult.affectedRows || 0,
            lastInsertRowid: mysqlResult.insertId || null
          }
        } finally {
          connection.release()
        }
      },
      
      async get(...args: any[]) {
        const connection = await pool.getConnection()
        try {
          // Filter out undefined values and replace with null for MySQL
          const filteredArgs = args.map(arg => arg === undefined ? null : arg)
          const [rows] = await connection.execute(sql, filteredArgs)
          const results = rows as any[]
          return results[0] || null
        } finally {
          connection.release()
        }
      },
      
      async all(...args: any[]) {
        const connection = await pool.getConnection()
        try {
          // Filter out undefined values and replace with null for MySQL
          const filteredArgs = args.map(arg => arg === undefined ? null : arg)
          const [rows] = await connection.execute(sql, filteredArgs)
          return rows as any[]
        } finally {
          connection.release()
        }
      }
    }
  }
}

// MySQL only - no SQLite support

// Initialize database schema
export async function initializeDatabase() {
  try {
    await db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        sub VARCHAR(255) PRIMARY KEY,
        email VARCHAR(255),
        name VARCHAR(255),
        blocked TINYINT DEFAULT 0,
        active TINYINT DEFAULT 0
      );
      
      CREATE TABLE IF NOT EXISTS \`groups\` (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) UNIQUE
      );
      
      CREATE TABLE IF NOT EXISTS group_members (
        group_id INT,
        user_sub VARCHAR(255),
        PRIMARY KEY (group_id, user_sub),
        FOREIGN KEY (group_id) REFERENCES \`groups\`(id) ON DELETE CASCADE,
        FOREIGN KEY (user_sub) REFERENCES users(sub) ON DELETE CASCADE
      );
      
      CREATE TABLE IF NOT EXISTS permissions (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category VARCHAR(255) DEFAULT 'general',
        is_active TINYINT DEFAULT 1
      );
      
      CREATE TABLE IF NOT EXISTS roles (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        is_active TINYINT DEFAULT 1,
        is_builtin TINYINT DEFAULT 0
      );
      
      CREATE TABLE IF NOT EXISTS role_permissions (
        role_id VARCHAR(255),
        permission_id VARCHAR(255),
        PRIMARY KEY (role_id, permission_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
      );
      
      CREATE TABLE IF NOT EXISTS user_roles (
        user_sub VARCHAR(255),
        role_id VARCHAR(255),
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (user_sub, role_id),
        FOREIGN KEY (user_sub) REFERENCES users(sub) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
      );
      
      CREATE TABLE IF NOT EXISTS sources (
        id INT AUTO_INCREMENT PRIMARY KEY,
        kind VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        config TEXT NOT NULL
      );
      
      CREATE TABLE IF NOT EXISTS user_groups (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active TINYINT DEFAULT 1
      );
      
      CREATE TABLE IF NOT EXISTS user_group_members (
        user_group_id INT,
        user_sub VARCHAR(255),
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (user_group_id, user_sub),
        FOREIGN KEY (user_group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
        FOREIGN KEY (user_sub) REFERENCES users(sub) ON DELETE CASCADE
      );
      
      CREATE TABLE IF NOT EXISTS policies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subject_type VARCHAR(255) NOT NULL,
        subject_id VARCHAR(255),
        source_id INT,
        path_prefix VARCHAR(255) DEFAULT '/',
        permission VARCHAR(255) NOT NULL,
        effect VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NULL,
        FOREIGN KEY (permission) REFERENCES permissions(id) ON DELETE CASCADE,
        FOREIGN KEY (source_id) REFERENCES sources(id) ON DELETE CASCADE
      );
      
      CREATE TABLE IF NOT EXISTS audit_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_sub VARCHAR(255) NOT NULL,
        action VARCHAR(255) NOT NULL,
        resource_type VARCHAR(255) NOT NULL,
        resource_id VARCHAR(255),
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_sub) REFERENCES users(sub) ON DELETE CASCADE
      );
    `)
    
    console.log('Database schema initialized successfully')
  } catch (error) {
    console.error('Failed to initialize database schema:', error)
    throw error
  }
}

export async function seedDefaultPermissions() {
  const permissions = [
    { id: 'files.read', name: 'Read Files', description: 'View and download files', category: 'files' },
    { id: 'files.write', name: 'Write Files', description: 'Upload and modify files', category: 'files' },
    { id: 'files.delete', name: 'Delete Files', description: 'Delete files and folders', category: 'files' },
    { id: 'files.share', name: 'Share Files', description: 'Create sharing links', category: 'files' },
    { id: 'sources.read', name: 'Read Sources', description: 'View source configurations', category: 'sources' },
    { id: 'sources.write', name: 'Write Sources', description: 'Create and modify sources', category: 'sources' },
    { id: 'sources.delete', name: 'Delete Sources', description: 'Remove sources', category: 'sources' },
    { id: 'users.read', name: 'Read Users', description: 'View user information', category: 'users' },
    { id: 'users.write', name: 'Write Users', description: 'Modify user information', category: 'users' },
    { id: 'users.delete', name: 'Delete Users', description: 'Remove users', category: 'users' },
    { id: 'groups.read', name: 'Read Groups', description: 'View groups', category: 'groups' },
    { id: 'groups.write', name: 'Write Groups', description: 'Create and modify groups', category: 'groups' },
    { id: 'groups.delete', name: 'Delete Groups', description: 'Remove groups', category: 'groups' },
    { id: 'admin.full', name: 'Full Admin', description: 'Complete system access', category: 'admin' }
  ]
  
  const stmt = db.prepare('INSERT IGNORE INTO permissions (id, name, description, category) VALUES (?, ?, ?, ?)')
  for (const perm of permissions) {
    await stmt.run(perm.id, perm.name, perm.description, perm.category)
  }
}

export async function seedDefaultRoles() {
  const roles = [
    { id: 'admin', name: 'Administrator', description: 'Full system access', is_builtin: 1 },
    { id: 'user', name: 'User', description: 'Basic file access', is_builtin: 1 },
    { id: 'viewer', name: 'Viewer', description: 'Read-only access', is_builtin: 1 }
  ]
  
  const roleStmt = db.prepare('INSERT IGNORE INTO roles (id, name, description, is_builtin) VALUES (?, ?, ?, ?)')
  for (const role of roles) {
    await roleStmt.run(role.id, role.name, role.description, role.is_builtin)
  }
  
  // Assign permissions to roles
  const permStmt = db.prepare('INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)')
  
  // Admin gets all permissions
  const adminPerms = ['files.read', 'files.write', 'files.delete', 'files.share', 'sources.read', 'sources.write', 'sources.delete', 'users.read', 'users.write', 'users.delete', 'groups.read', 'groups.write', 'groups.delete', 'admin.full']
  for (const perm of adminPerms) {
    await permStmt.run('admin', perm)
  }
  
  // User gets basic file permissions
  const userPerms = ['files.read', 'files.write', 'files.share']
  for (const perm of userPerms) {
    await permStmt.run('user', perm)
  }
  
  // Viewer gets read-only permissions
  const viewerPerms = ['files.read']
  for (const perm of viewerPerms) {
    await permStmt.run('viewer', perm)
  }
}

export async function seedDefaultSources(fsRoot: string, s3?: any) {
  // Check if sources already exist to prevent duplicates
  const existingCount = await db.prepare('SELECT COUNT(*) as count FROM sources').get() as any
  if (existingCount.count > 0) {
    console.log('Sources already exist, skipping seeding')
    return
  }
  
  console.log('Seeding default sources...')
  const stmt = db.prepare('INSERT INTO sources (kind, name, config) VALUES (?, ?, ?)')
  await stmt.run('fs', 'Local Files', JSON.stringify({ root: fsRoot }))
  
  // Only add S3 source if we have valid S3 configuration
  if (s3 && s3.bucket) {
    // Filter out undefined values from s3 config
    const s3Config = Object.fromEntries(
      Object.entries(s3).filter(([_, value]) => value !== undefined)
    )
    await stmt.run('s3', 'S3 Storage', JSON.stringify(s3Config))
  }
  console.log('Default sources seeded successfully')
}

export async function getDefaultSourceIdByKind(kind: string) {
  const stmt = db.prepare('SELECT id FROM sources WHERE kind = ? LIMIT 1')
  const row = await stmt.get(kind)
  return row?.id || null
}
