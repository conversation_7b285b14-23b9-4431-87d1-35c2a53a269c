import 'dotenv/config'
import mysql from 'mysql2/promise'
import fs from 'fs'
import path from 'path'

const dataDir = path.resolve(process.cwd(), 'data')
if (!fs.existsSync(dataDir)) fs.mkdirSync(dataDir, { recursive: true })

// MySQL connection configuration
const dbConfig = {
  host: process.env.DB_HOST || 'mysql',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root_password',
  database: process.env.DB_NAME || 'agrizy_files_db',
  port: parseInt(process.env.DB_PORT || '3306'),
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : undefined,
  connectTimeout: 60000
}

// Create connection pool
export const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
})

// Database wrapper to maintain compatibility with existing code
export const db = {
  async exec(sql: string) {
    const connection = await pool.getConnection()
    try {
      await connection.execute(sql)
    } finally {
      connection.release()
    }
  },
  
  prepare(sql: string) {
    return {
      async run(...args: any[]) {
        const connection = await pool.getConnection()
        try {
          // Filter out undefined values and replace with null for MySQL
          const filteredArgs = args.map(arg => arg === undefined ? null : arg)
          const [result] = await connection.execute(sql, filteredArgs)
          const mysqlResult = result as any
          return {
            changes: mysqlResult.affectedRows || 0,
            lastInsertRowid: mysqlResult.insertId || null
          }
        } finally {
          connection.release()
        }
      },
      
      async get(...args: any[]) {
        const connection = await pool.getConnection()
        try {
          // Filter out undefined values and replace with null for MySQL
          const filteredArgs = args.map(arg => arg === undefined ? null : arg)
          const [rows] = await connection.execute(sql, filteredArgs)
          const results = rows as any[]
          return results[0] || null
        } finally {
          connection.release()
        }
      },
      
      async all(...args: any[]) {
        const connection = await pool.getConnection()
        try {
          // Filter out undefined values and replace with null for MySQL
          const filteredArgs = args.map(arg => arg === undefined ? null : arg)
          const [rows] = await connection.execute(sql, filteredArgs)
          return rows as any[]
        } finally {
          connection.release()
        }
      }
    }
  }
}

// MySQL connection established - schema should be initialized via SQL files


