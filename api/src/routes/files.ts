import type { FastifyInstance } from 'fastify'
import { getProvider, getProviderForSource } from '../providers/index.js'
import { z } from 'zod'
import archiver from 'archiver'
import os from 'os'
import path from 'path'
import fs from 'fs'
import { getDefaultSourceIdByKind, db } from '../db.js'
import { allowedOps } from '../utils/policy.js'
import { auditLog, AUDIT_ACTIONS, AUDIT_RESOURCE_TYPES } from '../utils/audit.js'

export async function registerFileRoutes(app: FastifyInstance) {
  const listSchema = z.object({ provider: z.string().optional().default('fs'), path: z.string().optional().default('/'), page: z.coerce.number().optional(), size: z.coerce.number().optional(), sort: z.enum(['name', 'size', 'modifiedAt']).optional().default('name'), order: z.enum(['asc', 'desc']).optional().default('asc'), hideHidden: z.coerce.boolean().optional().default(false), sourceId: z.string().optional() })
  app.get('/v1/files', async (req: any) => {
    const q = listSchema.parse(req.query || {})
    const sourceId = q.sourceId ? parseInt(q.sourceId) : await getDefaultSourceIdByKind(q.provider)
    const p = sourceId ? getProviderForSource(sourceId) : getProvider(q.provider)
    const res = await p.list({ path: q.path, page: q.page, size: q.size, sort: q.sort, order: q.order })
    if (q.hideHidden) {
      res.items = res.items.filter(it => !it.name.startsWith('.'))
      res.total = res.items.length
    }
    // attach allowedOps per item
    const sub = req.user?.sub as string
    for (let i = 0; i < res.items.length; i++) {
      const ops = await allowedOps(sub, sourceId, res.items[i].path)
      res.items[i] = { ...res.items[i], allowedOps: ops }
    }
    return res
  })

  const statSchema = z.object({ provider: z.string().optional().default('fs'), path: z.string(), sourceId: z.string().optional() })
  app.get('/v1/files/stat', async (req: any) => {
    const q = statSchema.parse(req.query || {})
    const sourceId = q.sourceId ? parseInt(q.sourceId) : await getDefaultSourceIdByKind(q.provider)
    const p = sourceId ? getProviderForSource(sourceId) : getProvider(q.provider)
    const info = await p.stat(q.path)
    const ops = await allowedOps(req.user?.sub as string, sourceId, info.path)
    return { ...info, allowedOps: ops }
  })

  const mkdirSchema = z.object({ provider: z.string().optional().default('fs'), path: z.string(), type: z.literal('dir').optional().default('dir'), sourceId: z.string().optional() })
  app.post('/v1/files', async (req) => {
    const body = mkdirSchema.parse(req.body || {})
    const sourceId = body.sourceId ? parseInt(body.sourceId) : undefined
    const p = sourceId ? getProviderForSource(sourceId) : getProvider(body.provider)
    await p.mkdir(body.path)
    return { ok: true }
  })

  const moveCopySchema = z.object({ provider: z.string().optional().default('fs'), src: z.string(), dst: z.string(), sourceId: z.string().optional() })
  app.put('/v1/files/move', async (req: any) => {
    const body = moveCopySchema.parse(req.body || {})
    const sourceId = body.sourceId ? parseInt(body.sourceId) : undefined
    const p = sourceId ? getProviderForSource(sourceId) : getProvider(body.provider)
    await p.move(body.src, body.dst)
    
    // Audit log the file move
    await auditLog(
      req.user.sub,
      AUDIT_ACTIONS.FILE_MOVE,
      AUDIT_RESOURCE_TYPES.FILE,
      body.src,
      { src: body.src, dst: body.dst, sourceId },
      req.ip,
      req.headers['user-agent']
    )
    
    return { ok: true }
  })

  app.put('/v1/files/copy', async (req: any) => {
    const body = moveCopySchema.parse(req.body || {})
    const sourceId = body.sourceId ? parseInt(body.sourceId) : undefined
    const p = sourceId ? getProviderForSource(sourceId) : getProvider(body.provider)
    await p.copy(body.src, body.dst)
    
    // Audit log the file copy
    await auditLog(
      req.user.sub,
      AUDIT_ACTIONS.FILE_COPY,
      AUDIT_RESOURCE_TYPES.FILE,
      body.src,
      { src: body.src, dst: body.dst, sourceId },
      req.ip,
      req.headers['user-agent']
    )
    
    return { ok: true }
  })

  const deleteSchema = z.object({ provider: z.string().optional().default('fs'), path: z.string(), sourceId: z.string().optional() })
  app.delete('/v1/files', async (req: any) => {
    const body = deleteSchema.parse(req.body || {})
    const sourceId = body.sourceId ? parseInt(body.sourceId) : undefined
    const p = sourceId ? getProviderForSource(sourceId) : getProvider(body.provider)
    await p.delete(body.path)
    
    // Audit log the file deletion
    await auditLog(
      req.user.sub,
      AUDIT_ACTIONS.FILE_DELETE,
      AUDIT_RESOURCE_TYPES.FILE,
      body.path,
      { path: body.path, sourceId },
      req.ip,
      req.headers['user-agent']
    )
    
    return { ok: true }
  })

  const zipSchema = z.object({ provider: z.string().optional().default('fs'), paths: z.array(z.string()).min(1), dst: z.string(), sourceId: z.string().optional() })
  app.post('/v1/files/zip', async (req, reply) => {
    const body = zipSchema.parse(req.body || {})
    const sourceId = body.sourceId ? parseInt(body.sourceId) : undefined
    const p: any = sourceId ? getProviderForSource(sourceId) : getProvider(body.provider)
    if (typeof p.zip !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'zip not supported' } })
    await p.zip(body.paths, body.dst)
    return { ok: true }
  })

  const unzipSchema = z.object({ provider: z.string().optional().default('fs'), src: z.string(), dst: z.string(), sourceId: z.string().optional() })
  app.post('/v1/files/unzip', async (req, reply) => {
    const body = unzipSchema.parse(req.body || {})
    const sourceId = body.sourceId ? parseInt(body.sourceId) : undefined
    const p: any = sourceId ? getProviderForSource(sourceId) : getProvider(body.provider)
    if (typeof p.unzip !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'unzip not supported' } })
    await p.unzip(body.src, body.dst)
    return { ok: true }
  })

  const zipDlSchema = z.object({ provider: z.string().optional().default('fs'), path: z.string(), sourceId: z.string().optional() })
  app.get('/v1/files/zipdownload', async (req, reply) => {
    const q = zipDlSchema.parse((req as any).query || {})
    const sourceId = q.sourceId ? parseInt(q.sourceId) : undefined
    const p: any = sourceId ? getProviderForSource(sourceId) : getProvider(q.provider)
    if (typeof p.zip !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'zip not supported' } })
    const baseName = q.path.split('/').filter(Boolean).pop() || 'download'
    const tmp = path.join(os.tmpdir(), `${baseName}-${Date.now()}.zip`)
    await p.zip([q.path], tmp)
    reply.header('Content-Type', 'application/zip')
    reply.header('Content-Disposition', `attachment; filename="${baseName}.zip"`)
    return reply.send(fs.createReadStream(tmp))
  })


}
