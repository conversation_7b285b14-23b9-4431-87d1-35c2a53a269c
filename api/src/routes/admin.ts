import type { FastifyInstance } from 'fastify'
import { db } from '../db.js'
import { isAllowed, getSourcePermissionDetails } from '../utils/policy.js'
import { auditLog, getAuditLogs, AUDIT_ACTIONS, AUDIT_RESOURCE_TYPES } from '../utils/audit.js'

async function requireAdmin(sub: string) {
  if (!sub) return false // Return false for undefined sub
  const roles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub=?').all(sub) as any[]
  return roles.some(r => r.role_id === 'admin')
}

export async function registerAdminRoutes(app: FastifyInstance) {
  // Users list
  app.get('/v1/admin/users', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const users = await db.prepare('SELECT sub, email, name, blocked, active FROM users').all() as any[]
    const usersWithRoles = await Promise.all(users.map(async user => {
      const userRoles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub = ?').all(user.sub) as any[]
      const roles = userRoles.map(r => r.role_id)
      return {
        ...user,
        roles,
        is_admin: roles.includes('admin') // For backward compatibility
      }
    }))
    return usersWithRoles
  })

  // Get user source permissions
  app.get('/v1/admin/users/:sub/sources', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const userSub = req.params.sub
    const sources = await db.prepare('SELECT id, kind, name FROM sources').all() as any[]
    
    // Return all sources with their permission details (including implicit role-based permissions)
    return Promise.all(sources.map(async source => {
      const permissionDetails = await getSourcePermissionDetails(userSub, source.id)
      return {
        id: source.id,
        kind: source.kind,
        name: source.name,
        userRole: permissionDetails.userRole,
        permissions: permissionDetails.permissions
      }
    }))
  })

  app.post('/v1/admin/users/:sub/block', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    
    const targetSub = req.params.sub
    await db.prepare('UPDATE users SET blocked=? WHERE sub=?').run(1, targetSub)
    
    await auditLog(
      req.user.sub,
      AUDIT_ACTIONS.USER_BLOCK,
      AUDIT_RESOURCE_TYPES.USER,
      targetSub,
      { blocked: true },
      req.ip,
      req.headers['user-agent']
    )
    
    return { success: true }
  })

  app.post('/v1/admin/users/:sub/unblock', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    
    const targetSub = req.params.sub
    await db.prepare('UPDATE users SET blocked=? WHERE sub=?').run(0, targetSub)
    
    await auditLog(
      req.user.sub,
      AUDIT_ACTIONS.USER_UNBLOCK,
      AUDIT_RESOURCE_TYPES.USER,
      targetSub,
      { blocked: false },
      req.ip,
      req.headers['user-agent']
    )
    
    return { success: true }
  })

  // Sources management
  app.get('/v1/admin/sources', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const sources = await db.prepare('SELECT * FROM sources').all()
    return sources
  })

  app.post('/v1/admin/sources', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    
    const { kind, name, config } = req.body
    const result = await db.prepare('INSERT INTO sources (kind, name, config) VALUES (?, ?, ?)').run(kind, name, JSON.stringify(config))
    
    await auditLog(
      req.user.sub,
      'source_create',
      AUDIT_RESOURCE_TYPES.SOURCE,
      result.lastInsertRowid.toString(),
      { kind, name },
      req.ip,
      req.headers['user-agent']
    )
    
    return { id: result.lastInsertRowid, kind, name, config }
  })

  // User roles management
  app.get('/v1/admin/users/:sub/roles', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const userSub = req.params.sub
    const userRoles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub = ?').all(userSub) as any[]
    return { roles: userRoles.map(r => r.role_id) }
  })

  app.put('/v1/admin/users/:sub/roles', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const userSub = req.params.sub
    const { roles } = req.body
    
    // Remove existing roles
    await db.prepare('DELETE FROM user_roles WHERE user_sub = ?').run(userSub)
    
    // Add new roles
    for (const roleId of roles) {
      await db.prepare('INSERT INTO user_roles (user_sub, role_id) VALUES (?, ?)').run(userSub, roleId)
    }
    
    await auditLog(
      req.user.sub,
      'user_roles_update',
      AUDIT_RESOURCE_TYPES.USER,
      userSub,
      { roles },
      req.ip,
      req.headers['user-agent']
    )
    
    return { success: true }
  })

  // Roles management
  app.get('/v1/admin/roles', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const roles = await db.prepare('SELECT * FROM roles').all()
    return roles
  })

  app.get('/v1/admin/roles/:id', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const roleId = req.params.id
    const role = await db.prepare('SELECT * FROM roles WHERE id = ?').get(roleId)
    if (!role) return reply.code(404).send({ error: { code: 'NOT_FOUND' } })
    return role
  })

  // Permissions management
  app.get('/v1/admin/permissions', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const permissions = await db.prepare('SELECT * FROM permissions ORDER BY category, name').all()
    return permissions
  })

  app.get('/v1/admin/permissions/:id', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const permissionId = req.params.id
    const permission = await db.prepare('SELECT * FROM permissions WHERE id = ?').get(permissionId)
    if (!permission) return reply.code(404).send({ error: { code: 'NOT_FOUND' } })
    return permission
  })

  app.post('/v1/admin/permissions', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    
    const { id, name, description, category, is_active = 1 } = req.body
    
    try {
      const result = await db.prepare('INSERT INTO permissions (id, name, description, category, is_active) VALUES (?, ?, ?, ?, ?)').run(id, name, description, category, is_active)
      
      await auditLog(
        req.user.sub,
        'permission_create',
        AUDIT_RESOURCE_TYPES.PERMISSION,
        id,
        { name, description, category, is_active },
        req.ip,
        req.headers['user-agent']
      )
      
      return { id, name, description, category, is_active }
    } catch (error: any) {
      if (error.code === 'ER_DUP_ENTRY') {
        return reply.code(409).send({ error: { code: 'PERMISSION_EXISTS', message: 'Permission with this ID already exists' } })
      }
      throw error
    }
  })

  app.delete('/v1/admin/permissions/:id', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    
    const permissionId = req.params.id
    
    // Check if permission exists
    const permission = await db.prepare('SELECT * FROM permissions WHERE id = ?').get(permissionId)
    if (!permission) return reply.code(404).send({ error: { code: 'NOT_FOUND' } })
    
    // Delete the permission (cascade will handle related records)
    await db.prepare('DELETE FROM permissions WHERE id = ?').run(permissionId)
    
    await auditLog(
      req.user.sub,
      'permission_delete',
      AUDIT_RESOURCE_TYPES.PERMISSION,
      permissionId,
      { name: permission.name },
      req.ip,
      req.headers['user-agent']
    )
    
    return { success: true }
  })

  // Audit logs
  app.get('/v1/admin/audit', async (req: any, reply) => {
    if (!(await requireAdmin(req.user?.sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    
    const { limit = 100, offset = 0, user_sub, action, resource_type } = req.query as any
    const logs = getAuditLogs({ limit, offset, user_sub, action, resource_type })
    return logs
  })
}
