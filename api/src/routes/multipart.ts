import type { FastifyInstance } from 'fastify'
import { getProvider } from '../providers/index.js'
import { z } from 'zod'

export async function registerMultipartRoutes(app: FastifyInstance) {
  const initSchema = z.object({ provider: z.string().default('s3'), path: z.string(), contentType: z.string().optional() })
  app.post('/v1/uploads/multipart/init', async (req, reply) => {
    const body = initSchema.parse(req.body || {})
    const p: any = getProvider(body.provider)
    if (typeof p.initiateMultipart !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'Multipart not supported' } })
    return p.initiateMultipart(body.path, body.contentType)
  })

  const presignSchema = z.object({ provider: z.string().default('s3'), path: z.string(), uploadId: z.string(), partNumber: z.coerce.number().int().min(1), ttlSeconds: z.coerce.number().int().positive().default(900) })
  app.post('/v1/uploads/multipart/presignPart', async (req, reply) => {
    const body = presignSchema.parse(req.body || {})
    const p: any = getProvider(body.provider)
    if (typeof p.presignPart !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'Presign part not supported' } })
    return p.presignPart(body.path, body.uploadId, body.partNumber, body.ttlSeconds)
  })

  const completeSchema = z.object({ provider: z.string().default('s3'), path: z.string(), uploadId: z.string(), parts: z.array(z.object({ ETag: z.string(), PartNumber: z.number().int().min(1) })).min(1) })
  app.post('/v1/uploads/multipart/complete', async (req, reply) => {
    const body = completeSchema.parse(req.body || {})
    const p: any = getProvider(body.provider)
    if (typeof p.completeMultipart !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'Complete not supported' } })
    return p.completeMultipart(body.path, body.uploadId, body.parts)
  })

  const abortSchema = z.object({ provider: z.string().default('s3'), path: z.string(), uploadId: z.string() })
  app.post('/v1/uploads/multipart/abort', async (req, reply) => {
    const body = abortSchema.parse(req.body || {})
    const p: any = getProvider(body.provider)
    if (typeof p.abortMultipart !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'Abort not supported' } })
    return p.abortMultipart(body.path, body.uploadId)
  })
}

