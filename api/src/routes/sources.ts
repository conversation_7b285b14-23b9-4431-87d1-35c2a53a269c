import type { FastifyInstance } from 'fastify'
import { db } from '../db.js'
import { isAllowed } from '../utils/policy.js'

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3'
import { promises as fsp } from 'fs'
import { loadEnv } from '../env.js'

async function requireAdmin(sub: string) {
  if (!sub) return false // Return false for undefined sub
  const roles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub=?').all(sub) as any[]
  return roles.some(r => r.role_id === 'admin')
}

export async function registerSourceRoutes(app: FastifyInstance) {
  const env = loadEnv()
  // Database seeding is handled by SQL files during initialization

  app.get('/v1/sources', async (req: any) => {
    const rows = await db.prepare('SELECT id, kind, name, config FROM sources').all() as any[]
    // Filter by view permission
    const sub = req.user?.sub as string
    const filtered = []
    for (const r of rows) {
      if (await isAllowed(sub, Number(r.id), '/', 'sources.read')) {
        filtered.push(r)
      }
    }
    return filtered.map(r => ({ id: r.id, kind: r.kind, name: r.name }))
  })

  // Admin-only CRUD
  app.post('/v1/sources', async (req: any, reply) => {
    const sub = req.user?.sub as string
    if (!(await requireAdmin(sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const body = (req.body || {}) as any
    const kind = body.kind
    const name = body.name
    const config = JSON.stringify(body.config || {})
    const res = await db.prepare('INSERT INTO sources (kind, name, config) VALUES (?, ?, ?)').run(kind, name, config)
    
    const sourceId = res.lastInsertRowid as number
    
    return { id: sourceId }
  })

  app.put('/v1/sources/:id', async (req: any, reply) => {
    const sub = req.user?.sub as string
    if (!(await requireAdmin(sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const id = Number(req.params.id)
    const body = (req.body || {}) as any
    const name = body.name
    const config = JSON.stringify(body.config || {})
    
    const result = await db.prepare(`
      UPDATE sources 
      SET name = ?, config = ? 
      WHERE id = ?
    `).run(name, config, id)
    
    return { ok: true }
  })

  app.delete('/v1/sources/:id', async (req: any, reply) => {
    const sub = req.user?.sub as string
    if (!(await requireAdmin(sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const id = Number(req.params.id)
    
    await db.prepare('DELETE FROM sources WHERE id=?').run(id)
    return { ok: true }
  })

  // Admin-only: Test access for a source
  app.post('/v1/sources/:id/test', async (req: any, reply) => {
    const sub = req.user?.sub as string
    if (!(await requireAdmin(sub))) return reply.code(403).send({ error: { code: 'FORBIDDEN' } })
    const id = Number(req.params.id)
    const row = await db.prepare('SELECT kind, config FROM sources WHERE id=?').get(id) as any
    if (!row) return reply.code(404).send({ error: { code: 'NOT_FOUND' } })
    const cfg = (()=>{ try { return JSON.parse(row.config || '{}') } catch { return {} } })()
    try {
      if (row.kind === 'fs') {
        const root = cfg.root || process.cwd()
        await fsp.access(root)
        return { ok: true, message: `FS root accessible: ${root}` }
      }
      if (row.kind === 's3') {
        const client = new S3Client({
          region: cfg.region,
          credentials: cfg.accessKeyId && cfg.secretAccessKey ? { accessKeyId: cfg.accessKeyId, secretAccessKey: cfg.secretAccessKey } : undefined,
          endpoint: cfg.endpoint || undefined,
          forcePathStyle: !!cfg.forcePathStyle,
        })
        await client.send(new ListObjectsV2Command({ Bucket: cfg.bucket, MaxKeys: 1, Prefix: cfg.rootPrefix || undefined }))
        return { ok: true, message: `S3 access OK: ${cfg.bucket}` }
      }
      return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'Unknown kind' } })
    } catch (e: any) {
      return reply.code(400).send({ error: { code: 'TEST_FAILED', message: e?.message || 'Failed' } })
    }
  })
}
