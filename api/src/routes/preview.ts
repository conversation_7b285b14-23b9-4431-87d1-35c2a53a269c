import type { FastifyInstance } from 'fastify'
import { z } from 'zod'
import { getProvider } from '../providers/index.js'
import * as mime from 'mime-types'

function classify(m?: string | false) {
  const mt = (m || '').toString()
  if (mt.startsWith('image/')) return 'image'
  if (mt === 'application/pdf') return 'pdf'
  if (mt.startsWith('text/') || mt === 'application/json') return 'text'
  return 'binary'
}

export async function registerPreviewRoutes(app: FastifyInstance) {
  const metaSchema = z.object({ provider: z.string().optional().default('fs'), path: z.string() })
  app.get('/v1/previews/meta', async (req) => {
    const q = metaSchema.parse(req.query || {})
    const p = getProvider(q.provider)
    const info = await p.stat(q.path)
    const mt = info.mime || mime.lookup(info.name) || undefined
    const kind = classify(mt)
    return {
      path: info.path,
      mime: mt,
      kind, // image | pdf | text | binary
      available: false, // generation pipeline TBD; this is a skeleton
      strategies: kind === 'image' ? ['thumbnail', 'inline'] : kind === 'text' ? ['inline'] : ['inline'],
    }
  })

  const thumbSchema = z.object({ provider: z.string().optional().default('fs'), path: z.string(), size: z.coerce.number().optional().default(256) })
  app.get('/v1/previews/thumbnail', async (_req, reply) => {
    // Placeholder for future implementation
    return reply.code(501).send({ error: { code: 'NOT_IMPLEMENTED', message: 'Thumbnail generation not implemented yet' } })
  })
}

