import type { FastifyInstance } from 'fastify'
import { getProvider } from '../providers/index.js'
import { z } from 'zod'

export async function registerPresignRoutes(app: FastifyInstance) {
  const upSchema = z.object({ provider: z.string().default('s3'), path: z.string(), ttlSeconds: z.coerce.number().int().positive().default(900) })
  app.post('/v1/uploads/presign', async (req, reply) => {
    const body = upSchema.parse(req.body || {})
    const p: any = getProvider(body.provider)
    if (typeof p.presign !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'presign not supported' } })
    const res = await p.presign(body.path, 'put', body.ttlSeconds)
    return res
  })

  const dlSchema = z.object({ provider: z.string().default('s3'), path: z.string(), ttlSeconds: z.coerce.number().int().positive().default(900) })
  app.post('/v1/downloads/presign', async (req, reply) => {
    const body = dlSchema.parse(req.body || {})
    const p: any = getProvider(body.provider)
    if (typeof p.presign !== 'function') return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'presign not supported' } })
    const res = await p.presign(body.path, 'get', body.ttlSeconds)
    return res
  })
}
