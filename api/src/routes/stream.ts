import type { FastifyInstance } from 'fastify'
import { getProvider } from '../providers/index.js'
import { z } from 'zod'

function parseRange(range: string, size: number): { start: number; end: number } | null {
  if (!range?.startsWith('bytes=')) return null
  const spec = range.replace('bytes=', '')
  const [startStr, endStr] = spec.split('-')
  let start = startStr ? parseInt(startStr, 10) : NaN
  let end = endStr ? parseInt(endStr, 10) : NaN
  if (Number.isNaN(start)) {
    // suffix bytes: e.g. bytes=-500
    const length = Number.isNaN(end) ? 0 : end
    if (!size) return null
    start = Math.max(0, size - length)
    end = size - 1
  } else if (Number.isNaN(end)) {
    // bytes=500-
    end = size ? size - 1 : start
  }
  if (start > end || start < 0 || (size && start >= size)) return null
  return { start, end }
}

function makeEtag(size?: number, modifiedAt?: string) {
  const m = modifiedAt ? Date.parse(modifiedAt) : 0
  return `"${size ?? 0}-${m}"`
}

const downloadQuery = z.object({
  provider: z.string().optional().default('fs'),
  path: z.string(),
})

export async function registerStreamRoutes(app: FastifyInstance) {
  // FS download with Range support
  app.get('/v1/files/download', async (req, reply) => {
    const q = downloadQuery.parse(req.query || {})
    if (q.provider !== 'fs') {
      return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'Use presigned download for non-FS providers' } })
    }
    const p = getProvider('fs')
    const info = await p.stat(q.path)
    if (info.kind !== 'file') return reply.code(400).send({ error: { code: 'INVALID', message: 'Path is not a file' } })

    const size = info.size ?? 0
    const etag = makeEtag(size, info.modifiedAt)
    reply.header('ETag', etag)
    const inm = req.headers['if-none-match'] as string | undefined
    if (inm && inm === etag) {
      return reply.code(304).send()
    }

    const range = (req.headers['range'] as string | undefined) || ''
    let re = parseRange(range, size)
    // If-Range: only honor range if validator matches
    const ifRange = (req.headers['if-range'] as string | undefined) || ''
    if (ifRange && ifRange !== etag) re = null

    reply.header('Accept-Ranges', 'bytes')
    if (re) {
      const { start, end } = re
      const chunkSize = end - start + 1
      // @ts-ignore optional method for FS provider
      const stream = (p as any).readStreamRange ? await (p as any).readStreamRange(q.path, start, end) : await p.readStream(q.path)
      reply.code(206)
      reply.header('Content-Range', `bytes ${start}-${end}/${size}`)
      reply.header('Content-Length', String(chunkSize))
      if (info.mime) reply.header('Content-Type', info.mime)
      return reply.send(stream)
    } else {
      const stream = await p.readStream(q.path) as any
      if (size) reply.header('Content-Length', String(size))
      if (info.mime) reply.header('Content-Type', info.mime)
      return reply.send(stream)
    }
  })

  // FS upload (stream request body)
  const uploadQuery = z.object({ provider: z.string().optional().default('fs'), path: z.string(), overwrite: z.coerce.boolean().optional().default(false) })
  app.post('/v1/files/upload', async (req, reply) => {
    const q = uploadQuery.parse(req.query || {})
    if (q.provider !== 'fs') {
      return reply.code(400).send({ error: { code: 'UNSUPPORTED', message: 'Use presigned upload for non-FS providers' } })
    }
    const p = getProvider('fs')
    const ws = await p.writeStream(q.path, { overwrite: q.overwrite })
    await new Promise<void>((resolve, reject) => {
      (req.raw as any).pipe(ws).on('finish', () => resolve()).on('error', reject)
    })
    return { ok: true }
  })
}
