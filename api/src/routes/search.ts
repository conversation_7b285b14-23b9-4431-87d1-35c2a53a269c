import type { FastifyInstance } from 'fastify'
import { z } from 'zod'
import { getProvider, getProviderForSource } from '../providers/index.js'

export async function registerSearchRoutes(app: FastifyInstance) {
  const schema = z.object({
    provider: z.string().optional().default('fs'),
    path: z.string().optional().default('/'),
    q: z.string().min(1),
    page: z.coerce.number().optional().default(1),
    size: z.coerce.number().optional().default(100),
    recursive: z.coerce.boolean().optional().default(true),
    kind: z.enum(['file', 'dir']).optional(),
    sourceId: z.string().optional(),
  })

  app.get('/v1/search', async (req, reply) => {
    const q = schema.parse(req.query || {})
    const sourceId = q.sourceId ? parseInt(q.sourceId) : undefined
    const p: any = sourceId ? getProviderForSource(sourceId) : getProvider(q.provider)

    // Draft implementation: FS supports simple name contains search.
    // Other providers return NOT_IMPLEMENTED for now.
    if (p.constructor?.name !== 'FSProvider') {
      return reply.code(501).send({
        error: { code: 'NOT_IMPLEMENTED', message: 'Search not implemented for this provider' },
      })
    }

    // Use provider.list to enumerate directory entries; recursively walk within limits.
    const term = q.q.toLowerCase()
    const max = q.size * 5 // gather a little extra for pagination stability
    const matches: any[] = []

    async function scan(dir: string, depth = 0) {
      if (matches.length >= max) return
      const res = await p.list({ path: dir, size: 1000 })
      for (const it of res.items) {
        if (!q.kind || it.kind === q.kind) {
          if (it.name.toLowerCase().includes(term)) matches.push(it)
        }
        if (q.recursive && it.kind === 'dir') {
          await scan(it.path, depth + 1)
          if (matches.length >= max) return
        }
      }
    }

    await scan(q.path)
    const total = matches.length
    const start = (q.page - 1) * q.size
    const items = matches.slice(start, start + q.size)
    return { items, page: q.page, size: q.size, total }
  })
}

