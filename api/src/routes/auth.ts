import type { FastifyInstance } from 'fastify'
import { OAuth2Client } from 'google-auth-library'
import { loadEnv } from '../env.js'
import { db } from '../db.js'
import { readAuthBearer, signToken, verifyToken, type User } from '../utils/auth.js'
import { auditLog, AUDIT_ACTIONS, AUDIT_RESOURCE_TYPES } from '../utils/audit.js'

const env = loadEnv()
const client = new OAuth2Client(env.google?.clientId)

export async function registerAuthRoutes(app: FastifyInstance) {
  app.post('/v1/auth/google', async (req, reply) => {
    const body = (req.body as any) || {}
    const idToken = body.idToken as string
    if (!idToken) return reply.code(400).send({ error: { code: 'BAD_REQUEST', message: 'idToken required' } })
    try {
      console.log('Verifying Google token:', { idToken: idToken?.substring(0, 50) + '...', clientId: env.google?.clientId })
      const ticket = await client.verifyIdToken({ idToken, audience: env.google?.clientId })
      const payload = ticket.getPayload()
      if (!payload) return reply.code(401).send({ error: { code: 'UNAUTHORIZED', message: 'invalid token' ,details:env.google?.clientId } })
      console.log('Google token verified successfully:', { email: payload.email, sub: payload.sub })
      const user: User = { sub: payload.sub!, email: payload.email || undefined, name: payload.name || undefined, picture: payload.picture || undefined }

      // Domain allowlist
      const allowed = (process.env.ALLOWED_DOMAINS || 'agrizy.in,agrizy.com')
        .split(',')
        .map((s) => s.trim().toLowerCase())
        .filter(Boolean)
      const email = (payload.email || '').toLowerCase()
      const domain = email.includes('@') ? email.split('@').pop()! : ''
      
      // Check if this is the admin user
      const isAdmin = (user.email || '').toLowerCase() === '<EMAIL>'
      
      // upsert user - admin is active by default, others are inactive
      await db.prepare('INSERT INTO users (sub, email, name, active) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE email=VALUES(email), name=VALUES(name)')
        .run(user.sub, user.email || null, user.name || null, isAdmin ? 1 : 0)
      
      // Now check domain restriction after user exists for audit logging
      if (!email || !allowed.includes(domain)) {
        // Audit log failed login due to domain restriction
        await auditLog(
          payload.sub!,
          AUDIT_ACTIONS.LOGIN_FAILURE,
          AUDIT_RESOURCE_TYPES.SESSION,
          undefined,
          { email, domain, reason: 'FORBIDDEN_DOMAIN' },
          req.ip,
          req.headers['user-agent']
        )
        return reply.code(403).send({ error: { code: 'FORBIDDEN_DOMAIN', message: 'Email domain not allowed' } })
      }
      
      // Check if user is active (after upsert to ensure user exists)
      const userRecord = await db.prepare('SELECT active, blocked FROM users WHERE sub = ?').get(user.sub) as any
      if (!userRecord.active) {
        // Audit log failed login due to inactive account
        await auditLog(
          user.sub,
          AUDIT_ACTIONS.LOGIN_FAILURE,
          AUDIT_RESOURCE_TYPES.SESSION,
          undefined,
          { email: user.email, reason: 'ACCOUNT_INACTIVE' },
          req.ip,
          req.headers['user-agent']
        )
        return reply.code(403).send({ error: { code: 'ACCOUNT_INACTIVE', message: 'Account is not active. Please contact an administrator.' } })
      }
      
      if (userRecord.blocked) {
        // Audit log failed login due to blocked account
        await auditLog(
          user.sub,
          AUDIT_ACTIONS.LOGIN_FAILURE,
          AUDIT_RESOURCE_TYPES.SESSION,
          undefined,
          { email: user.email, reason: 'ACCOUNT_BLOCKED' },
          req.ip,
          req.headers['user-agent']
        )
        return reply.code(403).send({ error: { code: 'ACCOUNT_BLOCKED', message: 'Account is blocked. Please contact an administrator.' } })
      }
      
      // Assign admin role if this is the admin user
      if (isAdmin) {
        await db.prepare('INSERT IGNORE INTO user_roles (user_sub, role_id) VALUES (?, ?)')
          .run(user.sub, 'admin')
      } else {
        // Assign viewer role if user has no roles
        const existingRoles = await db.prepare('SELECT COUNT(*) as count FROM user_roles WHERE user_sub = ?').get(user.sub) as any
        if (existingRoles.count === 0) {
          await db.prepare('INSERT INTO user_roles (user_sub, role_id) VALUES (?, ?)')
            .run(user.sub, 'viewer')
        }
      }
      
      // Audit log successful login
      await auditLog(
        user.sub,
        AUDIT_ACTIONS.LOGIN_SUCCESS,
        AUDIT_RESOURCE_TYPES.SESSION,
        undefined,
        { email: user.email, domain },
        req.ip,
        req.headers['user-agent']
      )
      
      const token = signToken(user, env.appSecret)
      const userRoles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub = ?').all(user.sub) as any[]
      const roles = userRoles.map(r => r.role_id)
      return reply.send({ token, user: { ...user, roles, is_admin: roles.includes('admin') } })
    } catch (e: any) {
      console.error('Google token verification error:', e)
      // Audit log failed login due to token verification failure
      await auditLog(
        'unknown',
        AUDIT_ACTIONS.LOGIN_FAILURE,
        AUDIT_RESOURCE_TYPES.SESSION,
        undefined,
        { reason: 'INVALID_TOKEN', error: e.message },
        req.ip,
        req.headers['user-agent']
      )
      return reply.code(401).send({ error: { code: 'UNAUTHORIZED', message: 'token verification failed' } })
    }
  })

  app.get('/v1/auth/me', async (req, reply) => {
    const token = readAuthBearer(req)
    if (!token) return reply.code(401).send({ error: { code: 'NO_AUTH', message: 'missing token' } })
    const user = verifyToken(token, env.appSecret)
    if (!user) return reply.code(401).send({ error: { code: 'INVALID', message: 'invalid token' } })
    const userRoles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub = ?').all(user.sub) as any[]
    const roles = userRoles.map(r => r.role_id)
    return reply.send({ user: { ...user, roles, is_admin: roles.includes('admin') } })
  })

  app.get('/v1/auth/user-actions', async (req, reply) => {
    const token = readAuthBearer(req)
    if (!token) return reply.code(401).send({ error: { code: 'NO_AUTH', message: 'missing token' } })
    const user = verifyToken(token, env.appSecret)
    if (!user) return reply.code(401).send({ error: { code: 'INVALID', message: 'invalid token' } })
    
    // Get user roles
    const userRoles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub = ?').all(user.sub) as any[]
    const roles = userRoles.map(r => r.role_id)
    
    // Define role-based default actions (what the frontend expects)
    const ROLE_ACTIONS = {
      admin: ['list', 'stat', 'read', 'write', 'move', 'copy', 'delete', 'share', 'zip', 'search'],
      editor: ['list', 'stat', 'read', 'write', 'move', 'copy', 'zip', 'search'],
      viewer: ['list', 'stat', 'read']
    }
    
    // Get actions based on user roles
    const actions = new Set<string>()
    for (const roleId of roles) {
      const roleActions = ROLE_ACTIONS[roleId as keyof typeof ROLE_ACTIONS] || ROLE_ACTIONS.viewer
      roleActions.forEach(action => actions.add(action))
    }
    
    return reply.send(Array.from(actions))
  })
}
