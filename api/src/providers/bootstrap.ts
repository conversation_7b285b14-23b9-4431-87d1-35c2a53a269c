import { registerProvider } from './index.js'
import { FSProvider } from './fs.js'
import { S3Provider } from './s3.js'
import { S3Client } from '@aws-sdk/client-s3'
import { loadEnv } from '../env.js'

export function registerDefaultProviders() {
  const env = loadEnv()

  // FS
  registerProvider('fs', new FSProvider(env.fsRoot))

  // S3 (optional)
  if (env.s3.bucket && env.s3.region) {
    const client = new S3Client({
      region: env.s3.region,
      credentials: env.s3.accessKeyId && env.s3.secretAccessKey ? {
        accessKeyId: env.s3.accessKeyId,
        secretAccessKey: env.s3.secretAccessKey,
      } : undefined,
      endpoint: env.s3.endpoint || undefined,
      forcePathStyle: env.s3.forcePathStyle || undefined,
    })
    registerProvider('s3', new S3Provider(client, env.s3.bucket!, env.s3.rootPrefix || ''))
  }
}
