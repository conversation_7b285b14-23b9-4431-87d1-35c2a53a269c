import {
  S3Client,
  ListObjectsV2Command,
  HeadObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  CopyObjectCommand,
  DeleteObjectCommand
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import type { ListParams, ListResult, Provider, FileInfo } from './Provider.js'
import { Readable } from 'stream'
import {
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  AbortMultipartUploadCommand,
} from '@aws-sdk/client-s3'

function joinKey(root: string, key: string) {
  const k = (key || '').replace(/^\/+/, '')
  const r = root.replace(/\/+$/, '')
  const joined = [r, k].filter(Boolean).join('/')
  return joined.replace(/\/+$/, '')
}

function toRel(root: string, key: string) {
  if (!root) return '/' + key
  const clean = key.startsWith(root) ? key.slice(root.length).replace(/^\/+/, '') : key
  return '/' + clean
}

export class S3Provider implements Provider {
  constructor(private client: S3Client, private bucket: string, private rootPrefix: string = '') {}

  async list(params: ListParams): Promise<ListResult> {
    const prefix = joinKey(this.rootPrefix, params.path || '').replace(/^\/+/, '')
    const listPrefix = prefix.endsWith('/') || prefix === '' ? prefix : prefix + '/'

    const cmd = new ListObjectsV2Command({
      Bucket: this.bucket,
      Prefix: listPrefix,
      Delimiter: '/',
      MaxKeys: params.size ?? 1000
    })
    const resp = await this.client.send(cmd)

    const items: FileInfo[] = []
    for (const p of resp.CommonPrefixes ?? []) {
      const key = p.Prefix as string
      if (key === listPrefix) continue
      items.push({
        name: key.replace(listPrefix, '').replace(/\/+$/, ''),
        path: toRel(this.rootPrefix, key),
        kind: 'dir'
      })
    }
    for (const o of resp.Contents ?? []) {
      const key = o.Key as string
      if (key === listPrefix) continue
      items.push({
        name: key.replace(listPrefix, ''),
        path: toRel(this.rootPrefix, key),
        kind: 'file',
        size: o.Size,
        modifiedAt: o.LastModified?.toISOString(),
        etag: o.ETag?.replaceAll('"', '')
      })
    }

    return {
      items,
      page: params.page ?? 1,
      size: params.size ?? items.length,
      total: (resp.KeyCount as number) ?? items.length
    }
  }

  async stat(p: string): Promise<FileInfo> {
    const key = joinKey(this.rootPrefix, p).replace(/^\/+/, '')
    try {
      const head = await this.client.send(new HeadObjectCommand({ Bucket: this.bucket, Key: key }))
      return {
        name: key.split('/').pop() || key,
        path: toRel(this.rootPrefix, key),
        kind: 'file',
        size: head.ContentLength ?? undefined,
        modifiedAt: head.LastModified?.toISOString(),
        mime: head.ContentType || undefined,
        etag: head.ETag?.replaceAll('"', '')
      }
    } catch (e: any) {
      if (e?.$metadata?.httpStatusCode === 404) {
        // try as directory by listing
        const list = await this.client.send(new ListObjectsV2Command({
          Bucket: this.bucket,
          Prefix: key.endsWith('/') ? key : key + '/',
          MaxKeys: 1
        }))
        if ((list.KeyCount ?? 0) > 0) {
          return { name: key.split('/').pop() || key, path: toRel(this.rootPrefix, key), kind: 'dir' }
        }
      }
      throw e
    }
  }

  async readStream(p: string) {
    const key = joinKey(this.rootPrefix, p).replace(/^\/+/, '')
    const { Body } = await this.client.send(new GetObjectCommand({ Bucket: this.bucket, Key: key }))
    if (Body instanceof Readable) return Body
    // in browsers Body can be a blob/arraybuffer, but here Node:
    return Body as any
  }

  async writeStream(p: string) {
    // For simplicity, we implement a PassThrough uploader
    const key = joinKey(this.rootPrefix, p).replace(/^\/+/, '')
    const { PassThrough } = await import('stream')
    const pt = new PassThrough()
    const put = new PutObjectCommand({ Bucket: this.bucket, Key: key, Body: pt })
    this.client.send(put).catch(() => pt.destroy())
    return pt
  }

  async copy(src: string, dst: string): Promise<void> {
    const s = joinKey(this.rootPrefix, src).replace(/^\/+/, '')
    const d = joinKey(this.rootPrefix, dst).replace(/^\/+/, '')
    await this.client.send(new CopyObjectCommand({ Bucket: this.bucket, CopySource: `${this.bucket}/${s}`, Key: d }))
  }

  async move(src: string, dst: string): Promise<void> {
    await this.copy(src, dst)
    await this.delete(src)
  }

  async mkdir(p: string): Promise<void> {
    const key = joinKey(this.rootPrefix, p).replace(/^\/+/, '')
    const folderKey = key.endsWith('/') ? key : key + '/'
    await this.client.send(new PutObjectCommand({ Bucket: this.bucket, Key: folderKey, Body: '' }))
  }

  async delete(p: string): Promise<void> {
    const key = joinKey(this.rootPrefix, p).replace(/^\/+/, '')
    await this.client.send(new DeleteObjectCommand({ Bucket: this.bucket, Key: key }))
  }

  async zip(): Promise<void> { throw new Error('Not implemented') }
  async unzip(): Promise<void> { throw new Error('Not implemented') }

  async presign(path: string, action: 'get' | 'put', ttlSeconds: number) {
    const key = joinKey(this.rootPrefix, path).replace(/^\/+/, '')
    if (action === 'get') {
      const cmd = new GetObjectCommand({ Bucket: this.bucket, Key: key })
      const url = await getSignedUrl(this.client, cmd, { expiresIn: ttlSeconds })
      return { url }
    } else {
      const cmd = new PutObjectCommand({ Bucket: this.bucket, Key: key })
      const url = await getSignedUrl(this.client, cmd, { expiresIn: ttlSeconds })
      return { url }
    }
  }

  async initiateMultipart(path: string, contentType?: string) {
    const key = joinKey(this.rootPrefix, path).replace(/^\/+/, '')
    const cmd = new CreateMultipartUploadCommand({ Bucket: this.bucket, Key: key, ContentType: contentType })
    const res = await this.client.send(cmd)
    return { uploadId: res.UploadId!, key }
  }

  async presignPart(path: string, uploadId: string, partNumber: number, ttlSeconds: number) {
    const key = joinKey(this.rootPrefix, path).replace(/^\/+/, '')
    const cmd = new UploadPartCommand({ Bucket: this.bucket, Key: key, UploadId: uploadId, PartNumber: partNumber })
    const url = await getSignedUrl(this.client, cmd, { expiresIn: ttlSeconds })
    return { url }
  }

  async completeMultipart(path: string, uploadId: string, parts: { ETag: string; PartNumber: number }[]) {
    const key = joinKey(this.rootPrefix, path).replace(/^\/+/, '')
    const cmd = new CompleteMultipartUploadCommand({
      Bucket: this.bucket,
      Key: key,
      UploadId: uploadId,
      MultipartUpload: { Parts: parts },
    })
    await this.client.send(cmd)
    return { ok: true }
  }

  async abortMultipart(path: string, uploadId: string) {
    const key = joinKey(this.rootPrefix, path).replace(/^\/+/, '')
    const cmd = new AbortMultipartUploadCommand({ Bucket: this.bucket, Key: key, UploadId: uploadId })
    await this.client.send(cmd)
    return { ok: true }
  }
}
