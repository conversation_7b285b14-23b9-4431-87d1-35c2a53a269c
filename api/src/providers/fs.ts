import { promises as fsp } from 'fs'
import fs from 'fs'
import path from 'path'
import type { ListParams, ListResult, Provider, FileInfo } from './Provider.js'
import archiver from 'archiver'
import unzipper from 'unzipper'
import { pipeline } from 'stream/promises'
import * as mime from 'mime-types'

function ensureWithinRoot(root: string, p: string) {
  const clean = path.normalize(p || '/')
  const full = path.resolve(root, '.' + (clean.startsWith('/') ? clean : '/' + clean))
  const resolvedRoot = path.resolve(root)
  if (!full.startsWith(resolvedRoot)) throw new Error('Path escapes root')
  return full
}

function toRel(root: string, absolute: string) {
  const rel = path.relative(root, absolute)
  return '/' + rel.split(path.sep).join('/')
}

export class FSProvider implements Provider {
  constructor(private root: string) {}

  async list(params: ListParams): Promise<ListResult> {
    const dirPath = ensureWithinRoot(this.root, params.path || '/')
    const ents = await fsp.readdir(dirPath, { withFileTypes: true })
    const items: FileInfo[] = []
    for (const e of ents) {
      const ap = path.join(dirPath, e.name)
      const rel = toRel(this.root, ap)
      try {
        const st = await fsp.stat(ap)
        items.push({
          name: e.name,
          path: rel,
          kind: e.isDirectory() ? 'dir' : 'file',
          size: e.isDirectory() ? undefined : st.size,
          modifiedAt: st.mtime.toISOString(),
          mime: e.isDirectory() ? undefined : mime.lookup(e.name) || undefined,
        })
      } catch {
        // Skip entries we cannot stat (broken symlink, permission, etc.)
        items.push({
          name: e.name,
          path: rel,
          kind: e.isDirectory() ? 'dir' : 'file',
        })
      }
    }
    const page = params.page ?? 1
    const size = params.size ?? items.length
    // simple sort
    const field = params.sort || 'name'
    const order = params.order || 'asc'
    items.sort((a: any, b: any) => {
      const va = a[field] ?? ''
      const vb = b[field] ?? ''
      const cmp = va < vb ? -1 : va > vb ? 1 : 0
      return order === 'asc' ? cmp : -1 * cmp
    })
    const start = (page - 1) * size
    const paged = items.slice(start, start + size)
    return { items: paged, page, size, total: items.length }
  }

  async stat(p: string): Promise<FileInfo> {
    const ap = ensureWithinRoot(this.root, p)
    const st = await fsp.stat(ap)
    return {
      name: path.basename(ap),
      path: toRel(this.root, ap),
      kind: st.isDirectory() ? 'dir' : 'file',
      size: st.isDirectory() ? undefined : st.size,
      modifiedAt: st.mtime.toISOString(),
      mime: st.isDirectory() ? undefined : mime.lookup(ap) || undefined,
    }
  }

  async readStream(p: string) {
    const ap = ensureWithinRoot(this.root, p)
    return fs.createReadStream(ap)
  }

  // Optional: ranged read stream for FS downloads
  async readStreamRange(p: string, start: number, end: number) {
    const ap = ensureWithinRoot(this.root, p)
    return fs.createReadStream(ap, { start, end })
  }

  async writeStream(p: string, opts?: { overwrite?: boolean }) {
    const ap = ensureWithinRoot(this.root, p)
    await fsp.mkdir(path.dirname(ap), { recursive: true })
    if (!opts?.overwrite) {
      try {
        await fsp.access(ap)
        throw new Error('File exists')
      } catch {}
    }
    return fs.createWriteStream(ap)
  }

  async copy(src: string, dst: string): Promise<void> {
    const as = ensureWithinRoot(this.root, src)
    const ad = ensureWithinRoot(this.root, dst)
    await fsp.mkdir(path.dirname(ad), { recursive: true })
    // Node 16+: cp supports recursive
    await (fsp as any).cp(as, ad, { recursive: true, force: true })
  }

  async move(src: string, dst: string): Promise<void> {
    const as = ensureWithinRoot(this.root, src)
    const ad = ensureWithinRoot(this.root, dst)
    await fsp.mkdir(path.dirname(ad), { recursive: true })
    await fsp.rename(as, ad)
  }

  async mkdir(p: string): Promise<void> {
    const ap = ensureWithinRoot(this.root, p)
    await fsp.mkdir(ap, { recursive: true })
  }

  async delete(p: string): Promise<void> {
    const ap = ensureWithinRoot(this.root, p)
    await fsp.rm(ap, { recursive: true, force: true })
  }

  async zip(paths: string[], dst: string): Promise<void> {
    const outPath = ensureWithinRoot(this.root, dst)
    await fsp.mkdir(path.dirname(outPath), { recursive: true })
    const output = fs.createWriteStream(outPath)
    const archive = archiver('zip', { zlib: { level: 9 } })
    
    return new Promise((resolve, reject) => {
      (archive as any).on('warning', (err: any) => { 
        if (err.code !== 'ENOENT') reject(err) 
      })
      (archive as any).on('error', (err: any) => { reject(err) })
      
      output.on('close', () => resolve())
      archive.pipe(output)
      
      for (const p of paths) {
        const ap = ensureWithinRoot(this.root, p)
        const st = fs.statSync(ap)
        const name = path.basename(ap)
        if (st.isDirectory()) {
          (archive as any).directory(ap, name)
        } else {
          (archive as any).file(ap, { name })
        }
      }
      
      (archive as any).finalize()
    })
  }

  async unzip(src: string, dst: string): Promise<void> {
    const zipPath = ensureWithinRoot(this.root, src)
    const outDir = ensureWithinRoot(this.root, dst)
    await fsp.mkdir(outDir, { recursive: true })
    await pipeline(fs.createReadStream(zipPath), unzipper.Extract({ path: outDir }))
  }
}
