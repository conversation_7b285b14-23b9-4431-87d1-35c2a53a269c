export type FileKind = 'file' | 'dir'

export interface FileInfo {
  name: string
  path: string
  kind: FileKind
  size?: number
  modifiedAt?: string
  etag?: string
  mime?: string
  allowedOps?: string[]
}

export interface ListParams {
  path: string
  page?: number
  size?: number
  sort?: 'name' | 'size' | 'modifiedAt'
  order?: 'asc' | 'desc'
}

export interface ListResult {
  items: FileInfo[]
  page: number
  size: number
  total: number
}

export interface Provider {
  list(params: ListParams): Promise<ListResult>
  stat(path: string): Promise<FileInfo>
  readStream(path: string): Promise<NodeJS.ReadableStream>
  writeStream(
    path: string,
    opts?: { overwrite?: boolean }
  ): Promise<NodeJS.WritableStream>
  copy(src: string, dst: string): Promise<void>
  move(src: string, dst: string): Promise<void>
  mkdir(path: string): Promise<void>
  delete(path: string): Promise<void>
  zip(paths: string[], dst: string): Promise<void>
  unzip(src: string, dst: string): Promise<void>
  presign?: (
    path: string,
    action: 'get' | 'put',
    ttlSeconds: number
  ) => Promise<{ url: string; headers?: Record<string, string> }>
}

