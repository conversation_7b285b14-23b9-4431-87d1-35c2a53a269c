import type { Provider } from './Provider.js'
import { FSProvider } from './fs.js'
import { S3Provider } from './s3.js'
import { S3Client } from '@aws-sdk/client-s3'
import { db } from '../db.js'

const registry = new Map<string, Provider>()

export function registerProvider(name: string, provider: Provider) {
  registry.set(name, provider)
}

export function getProvider(name: string): Provider {
  const p = registry.get(name)
  if (!p) throw new Error(`Unknown provider: ${name}`)
  return p
}

export function getProviderForSource(sourceId: number): Provider {
  const row = db.prepare('SELECT kind, config FROM sources WHERE id=?').get(sourceId) as any
  if (!row) throw new Error(`Source not found: ${sourceId}`)
  
  const config = JSON.parse(row.config || '{}')
  
  if (row.kind === 'fs') {
    return new FSProvider(config.root || process.cwd())
  }
  
  if (row.kind === 's3') {
    const client = new S3Client({
      region: config.region,
      credentials: config.accessKeyId && config.secretAccessKey ? {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      } : undefined,
      endpoint: config.endpoint || undefined,
      forcePathStyle: config.forcePathStyle || undefined,
    })
    return new S3Provider(client, config.bucket, config.rootPrefix || '')
  }
  
  throw new Error(`Unsupported provider kind: ${row.kind}`)
}

