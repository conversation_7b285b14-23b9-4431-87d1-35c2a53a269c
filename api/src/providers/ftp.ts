import type { Provider, ListParams, ListResult, FileInfo } from './Provider.js'

export class FTPProvider implements Provider {
  async list(_params: ListParams): Promise<ListResult> { return { items: [], page: 1, size: 0, total: 0 } }
  async stat(_p: string): Promise<FileInfo> { throw new Error('Not implemented') }
  async readStream(_p: string): Promise<NodeJS.ReadableStream> { throw new Error('Not implemented') }
  async writeStream(_p: string): Promise<NodeJS.WritableStream> { throw new Error('Not implemented') }
  async copy(): Promise<void> { throw new Error('Not implemented') }
  async move(): Promise<void> { throw new Error('Not implemented') }
  async mkdir(): Promise<void> { throw new Error('Not implemented') }
  async delete(): Promise<void> { throw new Error('Not implemented') }
  async zip(): Promise<void> { throw new Error('Not implemented') }
  async unzip(): Promise<void> { throw new Error('Not implemented') }
}

