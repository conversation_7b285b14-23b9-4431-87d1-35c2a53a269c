// @ts-nocheck
// TODO: Fix async/await issues with MySQL migration
import { db } from '../db.js'

export type Action = string
export type Role = 'admin' | 'editor' | 'viewer'

// Role-based default permissions are now stored in the database via role_actions table

async function getActivePermissions(): Promise<string[]> {
  if (db.prepare) {
    // MySQL mode
    const rows = await db.prepare('SELECT id FROM permissions').all() as any[]
    return rows.map(r => r.id)
  } else {
    // JSON mode
    const fs = require('fs')
    const path = require('path')
    const dataDir = path.resolve(process.cwd(), 'data')
    const jsonPath = path.join(dataDir, 'app.json')
    if (fs.existsSync(jsonPath)) {
      const store = JSON.parse(fs.readFileSync(jsonPath, 'utf-8'))
      return store.permissions?.map((a: any) => a.id) || []
    }
    return []
  }
}

async function getUserRole(sub: string): Promise<Role> {
  if (!sub) return 'viewer' // Default fallback for undefined sub
  if (db.prepare) {
    // MySQL mode - check user_roles table
    const roles = await db.prepare('SELECT role_id FROM user_roles WHERE user_sub = ?').all(sub) as any[]
    // Return the highest priority role: admin > editor > viewer
    if (roles.some(r => r.role_id === 'admin')) return 'admin'
    if (roles.some(r => r.role_id === 'editor')) return 'editor'
    if (roles.some(r => r.role_id === 'viewer')) return 'viewer'
    return 'viewer' // Default fallback
  } else {
    // JSON mode
    const fs = require('fs')
    const path = require('path')
    const dataDir = path.resolve(process.cwd(), 'data')
    const jsonPath = path.join(dataDir, 'app.json')
    if (fs.existsSync(jsonPath)) {
      const store = JSON.parse(fs.readFileSync(jsonPath, 'utf-8'))
      const userRoles = store.user_roles?.filter((ur: any) => ur.user_sub === sub) || []
      // Return the highest priority role: admin > editor > viewer
      if (userRoles.some((ur: any) => ur.role_id === 'admin')) return 'admin'
      if (userRoles.some((ur: any) => ur.role_id === 'editor')) return 'editor'
      if (userRoles.some((ur: any) => ur.role_id === 'viewer')) return 'viewer'
      return 'viewer' // Default fallback
    }
    return 'viewer'
  }
}

async function getRoleDefaultPermissions(role: Role): Promise<string[]> {
  if (db.prepare) {
    // MySQL mode - get permissions for role from role_permissions junction table
    const rows = await db.prepare(`
      SELECT p.id FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ?
    `).all(role) as any[]
    return rows.map(r => r.id)
  } else {
    // JSON mode - get permissions for role from role_permissions array
    const fs = require('fs')
    const path = require('path')
    const dataDir = path.resolve(process.cwd(), 'data')
    const jsonPath = path.join(dataDir, 'app.json')
    if (fs.existsSync(jsonPath)) {
      const store = JSON.parse(fs.readFileSync(jsonPath, 'utf-8'))
      const rolePermissions = store.role_permissions?.filter((rp: any) => rp.role_id === role) || []
      const permissionIds = rolePermissions.map((rp: any) => rp.permission_id)
      const activePermissions = store.permissions?.filter((p: any) => permissionIds.includes(p.id)) || []
      return activePermissions.map((p: any) => p.id)
    }
    return []
  }
}

function getDefaultAllowedPermissions(): string[] {
  // Legacy function - now returns read category permissions
  if (db.prepare) {
    // MySQL mode
    const rows = db.prepare('SELECT id FROM permissions WHERE category IN ("read")').all() as any[]
    return rows.map(r => r.id)
  } else {
    // JSON mode
    const fs = require('fs')
    const path = require('path')
    const dataDir = path.resolve(process.cwd(), 'data')
    const jsonPath = path.join(dataDir, 'app.json')
    if (fs.existsSync(jsonPath)) {
      const store = JSON.parse(fs.readFileSync(jsonPath, 'utf-8'))
      return store.permissions?.filter((p: any) => p.category === 'read').map((p: any) => p.id) || []
    }
    return []
  }
}

async function userIsAdmin(sub: string) {
  return await getUserRole(sub) === 'admin'
}

async function userGroups(sub: string): Promise<number[]> {
  if (!sub) return [] // Return empty array for undefined sub
  const rows = await db.prepare('SELECT group_id FROM group_members WHERE user_sub=?').all(sub) as any[]
  return rows.map(r => r.group_id)
}

function policiesFor(sub: string, groups: number[], sourceId: number, path: string, action: Action) {
  const nowIso = new Date().toISOString()
  const rows = db.prepare(
    `SELECT * FROM policies WHERE source_id=? AND action=? AND (expires_at IS NULL OR expires_at > ?)`
  ).all(sourceId, action, nowIso) as any[]
  const ok: any[] = []
  for (const p of rows) {
    if (p.subject_type === 'all') ok.push(p)
    else if (p.subject_type === 'user' && p.subject_id === sub) ok.push(p)
    else if (p.subject_type === 'group' && groups.includes(Number(p.subject_id))) ok.push(p)
  }
  return ok.filter(p => (path || '/').startsWith(p.path_prefix || '/'))
}

export async function isAllowed(sub: string, sourceId: number, path: string, action: Action) {
  if (!sub) return false // Deny access for undefined sub
  if (await userIsAdmin(sub)) return true
  const groups = await userGroups(sub)
  const pols = policiesFor(sub, groups, sourceId, path, action)
  const hasDeny = pols.some(p => p.effect === 'deny')
  if (hasDeny) return false
  const hasAllow = pols.some(p => p.effect === 'allow')
  if (hasAllow) return true
  // Role-based default policy
  const userRole = await getUserRole(sub)
  const roleDefaults = await getRoleDefaultPermissions(userRole)
  return roleDefaults.includes(action)
}

export async function allowedOps(sub: string, sourceId: number, path: string) {
  const ops: Action[] = []
  const allPermissions = await getActivePermissions()
  for (const a of allPermissions) {
    if (await isAllowed(sub, sourceId, path, a)) ops.push(a)
  }
  return ops
}

// Get all allowed operations for a user (role-based defaults + explicit policies)
export async function getAllowedOps(sub: string): Promise<string[]> {
  const policies = policiesFor(sub, [], 0, '', '')
  const allowed = new Set<string>()
  
  // Add explicitly allowed actions
  policies.filter(p => p.effect === 'allow').forEach(p => {
    allowed.add(p.action)
  })
  
  // Add role-based default actions if no explicit denies
  const denied = new Set(policies.filter(p => p.effect === 'deny').map(p => p.action))
  const userRole = await getUserRole(sub)
  const roleDefaults = await getRoleDefaultPermissions(userRole)
  roleDefaults.forEach(action => {
    if (!denied.has(action)) {
      allowed.add(action)
    }
  })
  
  return Array.from(allowed)
}

// Get detailed permission information for a user on a specific source
export async function getSourcePermissionDetails(sub: string, sourceId: number) {
  const userRole = await getUserRole(sub)
  const groups = await userGroups(sub)
  const allActions = await getActivePermissions()
  
  // Get explicit policies for this source
  let explicitPolicies: { action: string, effect: 'allow' | 'deny' }[] = []
  
  if (groups.length > 0) {
    explicitPolicies = await db.prepare(`
      SELECT action, effect FROM policies 
      WHERE (subject_type = 'user' AND subject_id = ? OR subject_type = 'group' AND subject_id IN (${groups.map(() => '?').join(',')})) 
      AND source_id = ?
    `).all(sub, ...groups, sourceId) as { action: string, effect: 'allow' | 'deny' }[]
  } else {
    explicitPolicies = await db.prepare(`
      SELECT action, effect FROM policies 
      WHERE subject_type = 'user' AND subject_id = ? AND source_id = ?
    `).all(sub, sourceId) as { action: string, effect: 'allow' | 'deny' }[]
  }
  
  // Get role-based defaults
  const roleDefaults = await getRoleDefaultPermissions(userRole)
  
  const allPermissions = await getActivePermissions()
  const permissions: {
    action: string
    allowed: boolean
    source: 'explicit' | 'role' | 'denied'
    effect?: 'allow' | 'deny'
  }[] = []
  
  for (const permission of allPermissions) {
    const explicitPolicy = explicitPolicies.find(p => p.action === permission)
    
    if (explicitPolicy) {
      permissions.push({
        action: permission,
        allowed: explicitPolicy.effect === 'allow',
        source: 'explicit',
        effect: explicitPolicy.effect
      })
    } else {
      const roleAllows = roleDefaults.includes(permission)
      permissions.push({
        action: permission,
        allowed: roleAllows,
        source: roleAllows ? 'role' : 'denied'
      })
    }
  }
  
  return {
    userRole,
    permissions
  }
}

