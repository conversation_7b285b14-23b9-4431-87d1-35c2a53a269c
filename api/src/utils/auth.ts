import crypto from 'crypto'
import type { FastifyRequest } from 'fastify'

export type User = { sub: string; email?: string; name?: string; picture?: string }

export function signToken(user: User, secret: string) {
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url')
  const payload = Buffer.from(JSON.stringify({ user, iat: Math.floor(Date.now() / 1000) })).toString('base64url')
  const h = crypto.createHmac('sha256', secret).update(`${header}.${payload}`).digest('base64url')
  return `${header}.${payload}.${h}`
}

export function verifyToken(token: string, secret: string): User | null {
  const [header, payload, sig] = token.split('.')
  if (!header || !payload || !sig) return null
  const h = crypto.createHmac('sha256', secret).update(`${header}.${payload}`).digest('base64url')
  if (h !== sig) return null
  try {
    const data = JSON.parse(Buffer.from(payload, 'base64url').toString())
    return data.user as User
  } catch {
    return null
  }
}

export function readAuthBearer(req: FastifyRequest) {
  const hdr = req.headers['authorization'] || req.headers['Authorization']
  const v = Array.isArray(hdr) ? hdr[0] : hdr
  if (!v) return null
  const [scheme, token] = v.split(' ')
  if (!scheme || scheme.toLowerCase() !== 'bearer') return null
  return token || null
}

