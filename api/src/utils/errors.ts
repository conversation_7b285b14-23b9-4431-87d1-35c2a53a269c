import type { FastifyInstance } from 'fastify'

export function installErrorHandler(app: FastifyInstance) {
  app.setErrorHandler((err, _req, reply) => {
    const status = (err as any).statusCode || (err.name === 'ZodError' ? 400 : 500)
    const code = (err as any).code || (err.name === 'ZodError' ? 'VALIDATION_ERROR' : 'INTERNAL')
    const payload: any = {
      error: {
        code,
        message: err.message,
      },
    }
    if (err.name === 'ZodError') {
      payload.error.details = (err as any).issues
    }
    reply.code(status).send(payload)
  })
}

