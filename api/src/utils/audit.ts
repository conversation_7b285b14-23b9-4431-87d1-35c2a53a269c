import { db } from '../db.js'

export interface AuditLogEntry {
  id?: number
  user_sub: string
  action: string
  resource_type: string
  resource_id?: string
  details?: any
  ip_address?: string
  user_agent?: string
  timestamp: string
}

/**
 * Log an audit event for sensitive operations
 * @param user_sub - User identifier
 * @param action - Action performed (e.g., 'share_create', 'share_revoke', 'delete', 'elevate')
 * @param resource_type - Type of resource (e.g., 'share', 'file', 'user', 'policy')
 * @param resource_id - Optional resource identifier
 * @param details - Optional additional details as object
 * @param ip_address - Optional IP address
 * @param user_agent - Optional user agent string
 */
export async function auditLog(
  user_sub: string,
  action: string,
  resource_type: string,
  resource_id?: string,
  details?: any,
  ip_address?: string,
  user_agent?: string
): Promise<void> {
  try {
    const detailsJson = details ? JSON.stringify(details) : null
    
    await db.prepare(`
      INSERT INTO audit_logs (user_sub, action, resource_type, resource_id, details, ip_address, user_agent)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(user_sub, action, resource_type, resource_id, detailsJson, ip_address, user_agent)
  } catch (error) {
    // If foreign key constraint fails, it means user doesn't exist yet
    // Log the error but don't crash the application
    console.warn('Failed to log audit entry:', error instanceof Error ? error.message : String(error))
  }
}

/**
 * Get audit logs with optional filtering
 * @param filters - Optional filters for querying logs
 * @returns Array of audit log entries
 */
export async function getAuditLogs(filters?: {
  user_sub?: string
  action?: string
  resource_type?: string
  resource_id?: string
  limit?: number
  offset?: number
}): Promise<AuditLogEntry[]> {
  let sql = 'SELECT * FROM audit_logs'
  const params: any[] = []
  const conditions: string[] = []
  
  if (filters?.user_sub) {
    conditions.push('user_sub = ?')
    params.push(filters.user_sub)
  }
  
  if (filters?.action) {
    conditions.push('action = ?')
    params.push(filters.action)
  }
  
  if (filters?.resource_type) {
    conditions.push('resource_type = ?')
    params.push(filters.resource_type)
  }
  
  if (filters?.resource_id) {
    conditions.push('resource_id = ?')
    params.push(filters.resource_id)
  }
  
  if (conditions.length > 0) {
    sql += ' WHERE ' + conditions.join(' AND ')
  }
  
  sql += ' ORDER BY created_at DESC'
  
  if (filters?.limit) {
    sql += ' LIMIT ?'
    params.push(filters.limit)
    
    if (filters?.offset) {
      sql += ' OFFSET ?'
      params.push(filters.offset)
    }
  }
  
  const rows = await db.prepare(sql).all(...params) as AuditLogEntry[]
  
  // Parse details JSON back to objects
  return rows.map(row => ({
    ...row,
    details: row.details ? JSON.parse(row.details) : null
  }))
}

/**
 * Audit actions for sensitive operations
 */
export const AUDIT_ACTIONS = {
  // Share operations
  SHARE_CREATE: 'share_create',
  SHARE_REVOKE: 'share_revoke',
  SHARE_UPDATE: 'share_update',
  SHARE_ACCESS: 'share_access',
  
  // File operations
  FILE_DELETE: 'file_delete',
  FILE_MOVE: 'file_move',
  FILE_COPY: 'file_copy',
  
  // User/Policy operations
  USER_BLOCK: 'user_block',
  USER_UNBLOCK: 'user_unblock',
  USER_ACTIVATE: 'user_activate',
  USER_DEACTIVATE: 'user_deactivate',
  USER_ROLE_CHANGE: 'user_role_change',
  POLICY_CREATE: 'policy_create',
  POLICY_DELETE: 'policy_delete',
  POLICY_UPDATE: 'policy_update',
  
  // Authentication
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILURE: 'login_failure',
  LOGOUT: 'logout',
  
  // Privilege escalation
  ADMIN_ACCESS: 'admin_access',
  ELEVATE_PRIVILEGE: 'elevate_privilege'
} as const

/**
 * Resource types for audit logging
 */
export const AUDIT_RESOURCE_TYPES = {
  SHARE: 'share',
  FILE: 'file',
  FOLDER: 'folder',
  USER: 'user',
  POLICY: 'policy',
  SOURCE: 'source',
  SESSION: 'session',
  PERMISSION: 'permission'
} as const