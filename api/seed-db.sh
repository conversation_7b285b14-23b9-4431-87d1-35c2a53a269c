#!/bin/bash

# Database Seeding Script for Agrizy File Browser
# This script sets up the MySQL database using Node.js seeding script

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Database seeding script for Agrizy File Browser"
    echo ""
    echo "Options:"
    echo "  -f, --force     Force re-seeding (drops existing data)"
    echo "  -h, --help      Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DB_HOST         Database host (default: mysql)"
    echo "  DB_PORT         Database port (default: 3306)"
    echo "  DB_NAME         Database name (default: agrizy_files_db)"
    echo "  DB_USER         Database user (default: root)"
    echo "  DB_PASSWORD     Database password (default: root_password)"
    echo ""
}

# Configuration
DB_HOST="${DB_HOST:-mysql}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-agrizy_file_browser}"
DB_USER="${DB_USER:-root}"
DB_PASSWORD="${DB_PASSWORD:-root_password}"
MYSQL_OPTS="--skip-ssl --default-auth=mysql_native_password"

# Parse command line arguments
FORCE_MODE=false
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE_MODE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    log_info "Starting database seeding process using Node.js..."
    
    # Set force mode environment variable if needed
    if [ "$FORCE_MODE" = true ]; then
        export FORCE_SEED=true
        log_warning "Force mode enabled. Will re-seed database even if data exists."
    fi
    
    # Wait for MySQL to be ready
    echo "⏳ Waiting for MySQL to be ready..."
    while ! mariadb -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" $MYSQL_OPTS -e "SELECT 1" >/dev/null 2>&1; do
      echo "   MySQL not ready, waiting..."
      sleep 2
    done
    echo "✅ MySQL is ready!"
    
    # Run the Node.js seeding script
    if node /app/seed-database.cjs; then
        log_success "Database seeding completed successfully!"
        
        echo ""
        log_success "=== DATABASE SETUP COMPLETE ==="
        log_info "Admin access:"
        log_info "  - Google OAuth: <EMAIL> (automatic admin role)"
        log_info "  - Local admin: <EMAIL> (emergency access)"
        log_info "Default source: Local Files (/app/data)"
        echo ""
    else
        log_error "Database seeding failed!"
        exit 1
    fi
}

# Security note
log_warning "Security Note: This script uses default credentials for development."
log_warning "In production, ensure proper database security and credential management."

# Run main function
main

log_success "Database seeding script completed!"
exit 0