# =====================================
# Agrizy File Browser Monorepo
# Vue 3 + TypeScript + Fastify + MySQL
# =====================================

# Dependencies and Package Managers
node_modules/
*/node_modules/
**node_modules/
.pnp.*
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
jspm_packages/
bower_components/

# Lock files (keep pnpm-lock.yaml for consistency)
package-lock.json
yarn.lock
# pnpm-lock.yaml - keep this for pnpm projects

# Debug and Log Files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
logs/
pids/
*.pid
*.seed
*.pid.lock

# Environment Files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*
!.env.example

# Build Outputs and Distribution
dist/
build/
out/
.output/
.nuxt/
.next/
.vuepress/dist/
.serverless/

# TypeScript and Build Cache
*.tsbuildinfo
.tsbuildinfo
tsconfig.tsbuildinfo

# Testing and Coverage
coverage/
*.lcov
.nyc_output/
junit.xml

# Vue/Vite Specific
.vite/
dist-ssr/
*.local
.cache/
.parcel-cache/
.temp/

# Database Files (MySQL dumps, local data)
*.sql.gz
*.dump
*.backup
data/
!api/data/.gitkeep
*.db
*.db-shm
*.db-wal
*.sqlite
*.sqlite3

# File Storage and Uploads
uploads/
temp/
tmp/
storage/
files/
!**/storage/.gitkeep
!**/uploads/.gitkeep

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
*.sublime-*
.project
.settings/
.classpath

# Temporary and Backup Files
*.tmp
*.temp
*.bak
*.backup
*.orig
*~

# Docker and Container Files
.dockerignore
docker-compose.override.yml
.docker/

# Cloud and Deployment
.firebase/
firebase-debug.log
.vercel/
.netlify/
.aws/
.terraform/
*.tfstate
*.tfstate.*

# SSL and Security
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx
secrets/

# AI Assistant Directories
.claude/
.serena/
.cursor/
.copilot/
.aider/
.continue/
.codeium/
.tabnine/
.kite/
.ai/
.anthropic/
.openai/
.trae/
.codex/

# Process Management
.backend.pid
.frontend.pid
.pm2/
pm2.config.js

# Monitoring and Analytics
.sentry/
.sentryclirc
newrelic.js
newrelic_agent.log

# Project Specific
# API specific ignores
api/uploads/
api/temp/
api/logs/

# Web specific ignores
web/dist/
web/.vite/

# Scripts and automation
scripts/logs/
*.log.txt

# Compressed files
*.tar.gz
*.zip
*.rar

# Test files
test-*.html

#Ignore vscode AI rules
.github/instructions/codacy.instructions.md
