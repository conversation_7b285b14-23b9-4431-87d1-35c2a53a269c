# Agrizy File Browser - Setup Guide

This guide provides step-by-step instructions for setting up the Agrizy File Browser development environment according to the project rules defined in [`.trae/rules/project_rules.md`](.trae/rules/project_rules.md).

## 🚀 Quick Start

### Prerequisites
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher
- **pnpm**: Version 8.0.0 or higher (recommended)

### 1. Initial Setup
```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd agrizy-file-browser

# Install all dependencies
npm run install:all

# Setup database
cd api && npm run migrate && cd ..
```

### 2. Development Workflow

#### Option A: Automated Start (Recommended)
```bash
# Start both frontend and backend with port management
npm run dev
```

This command will:
- Automatically terminate any existing processes on ports 3000 and 3030
- Start the backend on port 3030
- Start the frontend on port 3000
- Verify both services are running correctly

#### Option B: Manual Start
```bash
# Terminal 1: Start backend
cd api
npm run dev  # Runs on port 3030

# Terminal 2: Start frontend
cd web
npm run dev  # Runs on port 3000
```

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3030
- **Health Check**: http://localhost:3030/v1/health

## 🔧 Port Management

### Available Commands
```bash
# Check port status
npm run port:status

# Clean ports (terminate conflicting processes)
npm run port:clean

# Stop all services
npm run stop

# Restart all services
npm run restart
```

### Port Configuration
- **Frontend**: Port 3000 (MANDATORY - NO FALLBACK)
- **Backend**: Port 3030 (MANDATORY - NO FALLBACK)

⚠️ **Important**: The application will NOT fallback to alternative ports. If ports 3000 or 3030 are occupied, the port manager will terminate existing processes.

## 🛠 Development Commands

### Root Level Commands
```bash
npm run dev          # Start both services with port management
npm run start        # Alias for dev
npm run stop         # Stop all services
npm run restart      # Restart all services
npm run setup        # Complete initial setup
npm run install:all  # Install dependencies for both projects
npm run build:all    # Build both projects
```

### Individual Service Commands
```bash
npm run dev:frontend # Start only frontend
npm run dev:backend  # Start only backend
```

## 📁 Project Structure

```
agrizy-file-browser/
├── .trae/
│   └── rules/
│       └── project_rules.md    # Authoritative project configuration
├── scripts/
│   └── port-manager.sh         # Port management script
├── api/                        # Backend (Fastify + TypeScript)
│   ├── src/
│   ├── data/                   # SQLite database
│   └── package.json
├── web/                        # Frontend (Vue 3 + TypeScript)
│   ├── src/
│   └── package.json
├── package.json                # Root package.json with scripts
└── SETUP.md                    # This file
```

## 🔍 Troubleshooting

### Port Conflicts
If you encounter `EADDRINUSE` errors:

```bash
# Check what's using the ports
npm run port:status

# Clean the ports
npm run port:clean

# Try starting again
npm run dev
```

### Manual Port Cleanup
```bash
# Find processes using required ports
lsof -i :3000
lsof -i :3030

# Kill specific process
kill -9 <PID>

# Kill all processes on both ports
kill -9 $(lsof -ti:3000,3030)
```

### Common Issues

1. **Module Not Found**
   ```bash
   npm run install:all
   ```

2. **Database Locked**
   ```bash
   # Stop all services first
   npm run stop
   # Then restart
   npm run dev
   ```

3. **CORS Errors**
   - Verify backend is running on port 3030
   - Check that frontend is accessing http://localhost:3030

4. **Build Errors**
   ```bash
   # Clean install
   rm -rf api/node_modules web/node_modules
   npm run install:all
   ```

## 🚨 Critical Rules

1. **NEVER** use ports other than 3000 (frontend) and 3030 (backend)
2. **ALWAYS** use the port management script for development
3. **NEVER** commit database files to version control
4. **ALWAYS** follow the exact framework versions specified in project_rules.md
5. **NEVER** bypass the port cleanup protocol

## 📚 Additional Resources

- [Project Rules](.trae/rules/project_rules.md) - Complete configuration reference
- [API Documentation](api/openapi.yaml) - Backend API specification
- [Frontend Documentation](web/README.md) - Frontend-specific documentation

## 🆘 Getting Help

If you encounter issues not covered in this guide:

1. Check the [Project Rules](.trae/rules/project_rules.md) for configuration details
2. Run `npm run port:status` to diagnose port conflicts
3. Verify all prerequisites are installed with correct versions
4. Try a complete restart: `npm run stop && npm run dev`

---

**Last Updated**: $(date)
**Version**: 1.0.0