# PRD — Resumable & Large Uploads

## Problem & Goals
Support reliable, resumable uploads for large files across providers; prefer direct‑to‑S3 for performance.

## Functional Requirements
- Resumable protocol: TUS or custom chunk API (id, chunkIndex, size, checksum).
- Server‑side assembly for FS/SFTP/FTP; S3 multipart (init, uploadPart, complete, abort).
- Resume, pause, retry with exponential backoff; integrity via checksums.
- Client: progress, remaining time, max size validation, concurrent chunks.

## API Endpoints
- `POST /v1/uploads/init` → uploadId & strategy.
- `POST /v1/uploads/:id/chunk` (FS/SFTP/FTP), `POST /v1/uploads/:id/presign` (S3 part).
- `POST /v1/uploads/:id/complete`, `DELETE /v1/uploads/:id` abort.

## Non‑Functional
- Handle 5GB+ files; memory‑safe streaming; configurable concurrency; temp storage cleanup.

## Acceptance Criteria
- Upload interruption resumes successfully; integrity verified; S3 multipart path validated.
- Backpressure doesn’t exceed memory; cleanup removes abandoned sessions.

## Dependencies
- Providers, RBAC (write permission), rate limiting, antivirus (optional later).

