# PRD — CRUD, Search, Previews, Zip

## Problem & Goals
Deliver core file operations with fast listing/search, rich previews, and convenient archive handling.

## Functional Requirements
- CRUD: list, stat, create, upload/download, move/copy/rename, delete.
- Search: by name, extension, size/date; server‑side pagination & sort.
- Previews: image (thumb + view), PDF (view), text (syntax highlight), audio/video (stream with ranges).
- Zip/Unzip: multi‑select zip download; unzip to destination; progress.
- Watermarking (optional): image/PDF overlay on preview/download when enabled.

## API Endpoints
- Files: `GET /v1/files`, `GET /v1/files/stat`, `POST /v1/files`, `PUT /v1/files/{move|copy|rename}`, `DELETE /v1/files`.
- Search: `GET /v1/search?q=&filters=&page=&size=`.
- Previews: `GET /v1/preview?provider=&path=&w=&h=&fit=`; `GET /v1/stream?provider=&path=`.
- Archives: `POST /v1/files/zip`, `POST /v1/files/unzip`.

## UX Requirements
- Responsive list/table with breadcrumbs, toolbar, multi‑select, keyboard shortcuts.
- Non‑blocking operations with toasts; errors readable and actionable.

## Non‑Functional
- p95 list <= 500ms (1k entries) with pagination; previews cached; streaming with backpressure.

## Acceptance Criteria
- Consistent behavior across providers; previews render common formats; search stable under pagination.

## Dependencies
- Providers, uploads, RBAC, preview/watermark pipeline.

