# PRD — Storage Providers (FS, S3, SFTP, FTP)

## Problem & Goals
Unify access to Local FS, S3, SFTP, and FTP behind a single Provider interface with consistent semantics, performance, and security.

## Users
- End users performing file ops.
- Admins configuring provider credentials and policies.

## Functional Requirements
- Provider contract: `list(path, {page,size,sort})`, `stat(path)`, `readStream(path)`, `writeStream(path, opts)`, `copy(src,dst)`, `move(src,dst)`, `mkdir(path)`, `delete(path)`, `zip(paths,dst)`, `unzip(src,dst)`, `presign(path, action, ttl)` (optional).
- Path safety: prevent traversal; normalize; case handling.
- Metadata: name, path, type, size, etag/hash, modifiedAt, mime.
- Errors: consistent codes/messages (NOT_FOUND, FORBIDDEN, CONFLICT, RATE_LIMITED).

## Non‑Functional Requirements
- Pagination for large listings; streaming for reads/writes; resilient retries; timeouts.
- Security: scoped credentials; IAM least privilege; TLS only.

## Implementation Details
- Local FS: Node streams (`fs`, `stream/web`).
- S3: AWS SDK v3; multipart uploads; pre‑signed URLs for read/write; server‑side zip via temp bucket or streaming.
- SFTP: `ssh2-sftp-client`; chunked transfers; keep‑alive.
- FTP: `basic-ftp`; passive mode; fallback retries.
- Config: provider registry; env‑driven secrets; per‑provider root mapping.

## API Endpoints (Backend)
- `GET /v1/files?provider=&path=&page=&size=&sort=`
- `GET /v1/files/stat?provider=&path=`
- `POST /v1/files` (mkdir), `PUT /v1/files/move`, `PUT /v1/files/copy`, `DELETE /v1/files`
- `POST /v1/files/zip`, `POST /v1/files/unzip`
- `POST /v1/uploads/presign` (S3), `POST /v1/uploads/stream` (others)

## Acceptance Criteria
- Provider matrix tests cover all ops with consistent results.
- Large file (5GB) upload supported (S3 multipart, streamed for others).
- Listing p95 <= 500ms for 1k entries (local/s3) under pagination.
- No path traversal or credential leakage in logs/errors.

## Dependencies
- Implementation plan, RBAC policies, resumable upload service, zip/preview services.

