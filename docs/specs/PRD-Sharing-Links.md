# PRD — Sharing Links & Public Access

## Problem & Goals
Enable secure, flexible file/folder sharing via expiring, password‑protected, one‑time, and upload‑drop links with auditing and custom domains.

## Users
- Internal users creating/managing shares.
- External recipients consuming public links.
- Ad<PERSON> enforcing policies and reviewing audits.

## Functional Requirements
- Link types: expiring (TTL), password‑protected, one‑time download, view‑only, upload‑drop.
- Scope: file or folder; limits: max downloads, size cap; IP allow/deny; revoke anytime.
- Custom domains: map space/tenant → link host; HTTPS only.
- Auditing: creator, createdAt, expiresAt, revokedAt, downloadCount, first/last access, IP.

## Public Flow
- S3: pre‑sign on demand with short TTL; redirect client to S3.
- FS/SFTP/FTP: proxy stream with range support and throttling; apply watermarking if enabled.

## API Endpoints
- `POST /v1/shares` create; `GET /v1/shares/:id` read; `DELETE /v1/shares/:id` revoke; list `GET /v1/shares?owner=me`.
- Public: `GET /p/:token` resolve; `GET /p/:token/download` (or redirect); `POST /p/:token/upload` for drop.

## Security
- High‑entropy, non‑enumerable tokens; short defaults; password hashing (KDF); retry limits.
- Cleanup job: purge expired; rotate token on edits.

## Acceptance Criteria
- Create/revoke/edit flows; password prompts; expiry enforcement; counters updated accurately.
- S3 links never proxy large payloads (unless caps/watermarking requires proxy).
- Audits visible to creator/admin.

## Dependencies
- Providers, RBAC, watermark/preview services, custom domain routing.

