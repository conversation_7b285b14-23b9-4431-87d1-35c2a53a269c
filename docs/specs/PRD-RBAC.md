# PRD — RBAC & Access Control

## Problem & Goals
Provide minimal‑friction, secure authorization with roles and folder‑level ACLs, enforced consistently across API and UI.

## Users
- Admins (manage providers, policies, users, roles).
- Editors (full CRUD in allowed scopes).
- Viewers (read/preview/download only).

## Functional Requirements
- Roles: `admin`, `editor`, `viewer` (extensible).
- ACLs: path‑based rules per provider (allow/deny; recursive); exceptions at subtree level.
- Policies: operation checks (list, stat, read, write, move, copy, delete, share, zip, search).
- Sessions/JWT: short‑lived access token, optional refresh; CSRF for cookie sessions.
- UI Guards: hide/disable forbidden actions.

## Non‑Functional
- Single policy engine; O(1) decision via normalized path rules; cache with invalidation.

## API/Middleware
- Auth: `POST /v1/auth/login/logout/refresh`.
- Policy middleware wraps all file/share routes; emits `403` with reason codes.

## Auditing
- Record sensitive ops: share create/revoke, delete, elevate.

## Acceptance Criteria
- Deny by default; least privilege by config; policy cache hits >95% under load.
- E2E tests verify UI and API alignment for each role.

## Dependencies
- Provider registry, user directory (local table), session storage, logging.

