# BRD — Lightweight Modern File Browser (Node/TS)

## Vision & Goals
Deliver a modern, lightweight, self‑hosted file browser with first‑class provider support (Local FS, S3, SFTP, FTP), secure sharing links, and a clean, consistent UX. Optimize authorization, performance, and maintainability.

## Target Users
- Team users managing files across mixed storage.
- Admins configuring providers, policies, and branding.
- External recipients accessing shared links.

## In‑Scope (Baseline Must‑Haves)
- Providers: Local FS, S3, SFTP, FTP (pluggable adapter interface).
- Core CRUD: browse, preview, create, upload/download, move/copy/rename, delete, zip/unzip.
- Sharing Links: expiring/password/one‑time links; revoke; audit; custom link domains.
- Search: name, ext, size/date filters; pagination.
- Resumable Uploads: chunked, retryable; direct‑to‑S3 multipart.
- RBAC: roles + folder ACLs; UI guards.
- Watermarking: optional for image/PDF previews/downloads.

## Out‑of‑Scope (v1)
- Full text content indexing; complex DLP; antivirus; external identity providers (can follow).

## Non‑Functional Requirements
- Performance: p95 list <= 500ms for 1k entries (paginated); streaming downloads; uploads 5GB+.
- Security: OWASP best practices, signed URLs, rate limits, input validation, least privilege IAM.
- Reliability: graceful retries; idempotent writes; periodic cleanup for expired shares.
- Observability: structured logs, request tracing, essential metrics (latency/error rate).

## Success Metrics
- p95 API latency for list/download/upload; error rate < 0.5%.
- E2E task success (create+share+download) > 99% in CI.
- >80% critical-path test coverage; <5 min CI runtime.

## Constraints & Assumptions
- Backend: Node 20 + TypeScript + Fastify; DB: SQLite→Postgres; Prisma ORM.
- Frontend: Vue 3 + TS + Vite; Tailwind optional; i18n.
- Deploy: Docker; optional CDN/custom domains for links.

## High‑Level Milestones
1) Contracts & scaffolds; 2) Local FS + CRUD; 3) S3 + uploads; 4) Sharing links; 5) SFTP/FTP; 6) Search/zip/previews/watermark; 7) RBAC polish; 8) Perf/Sec/CI; 9) GA & docs.

