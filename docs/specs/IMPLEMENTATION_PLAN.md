# Implementation Plan — Node/TS Rewrite

## Tech Stack
- Backend: Node 20, TypeScript, Fastify, Zod, Prisma (SQLite→Postgres), Pino.
- Frontend: Vue 3 + TS, Vite, Pinia, Vue Router, Tailwind (optional), vue-i18n.
- Providers: AWS SDK v3 (S3), ssh2-sftp-client, basic-ftp, fs streams.
- Uploads: S3 multipart + chunked stream API; Vitest/Cypress for tests.

## Phases & Timeline (Sequential with Parallel Tracks)
1) Specs & Contracts (Week 1)
- Tasks: Finalize BRD/PRDs; define Provider interface; API paths; RBAC model.
- Output: OpenAPI draft; TypeScript interfaces; DB schema (Prisma).

2) Platform Scaffold (Week 1)
- Backend: Fastify app, Pino logging, error handling, health checks; Prisma + SQLite; migrations.
- Frontend: Vite + Vue 3 + TS; base layout, router, auth shell; fetch wrapper.

3) Providers & CRUD Foundations (Weeks 2–3)
- Implement Local FS provider; file listing/stat/CRUD endpoints; pagination; zip/unzip service.
- Frontend file list, toolbar, breadcrumbs, multi-select; basic previews.

4) RBAC Core (Week 3)
- Path ACL model; policy middleware; roles (admin/editor/viewer); UI guards.

5) S3 Provider & Uploads (Weeks 3–4)
- S3 adapter with presign + multipart; direct-to-S3 client; resumable fallback for others.

6) SFTP & FTP Providers (Week 4)
- Streamed transfers; retries; config pages; matrix tests.

7) Sharing Links (Week 5)
- Share model, token service, public routes, password/expiry/limits, audits; custom domain routing.
- Public link page (download/preview/upload-drop).

8) Previews & Watermarking (Week 5)
- Sharp-based pipeline; pdf-lib/ghostscript; caching; watermark toggle.

9) Search & Polish (Week 6)
- Server search with filters; UI search box; empty/error states; accessibility pass.

10) Perf/Security/CI (Week 6)
- Rate limits, CORS/Helmet, input validation; GitHub Actions (lint/test/build); Docker images; docs.

## Parallelization & Sub‑Agents
- Backend Agent: Phases 3–7 (providers, API, shares).
- Frontend Agent: Phases 3–9 (UI/UX, public pages).
- Auth/Security Agent: Phase 4 & 10 (RBAC, hardening).
- Uploads Agent: Phase 5 (multipart + resumables).
- DevOps Agent: Phases 2 & 10 (Docker, CI, domains/CDN).
- QA Agent: Continuous (matrix tests, e2e flows).
- Docs Agent: Continuous (guides, runbooks).

## Data Model (Initial)
- User(id, email, role, passwordHash?)
- Acl(id, provider, path, role, rule)
- Share(id, token, provider, path, permissions, expiresAt, passwordHash?, maxDownloads?, downloadCount, ipRules?, createdBy)
- UploadSession(id, strategy, state, metadata, expiresAt)

## Risks & Mitigations
- SFTP/FTP performance: conservative timeouts, retries, chunk size tuning.
- Watermark CPU: cache results, optional feature flag, background pre-gen.
- Large listings: paginate with cursor, server caps.

## Exit Criteria
- Provider matrix green; p95 latency targets met; e2e critical flows passing; docs published; versioned release artifacts.

