 Overview

  - Backend: Fastify + TypeScript under v2/api (REST /v1).
  - Frontend: Vue 3 + Vite under v2/web (SPA).
  - Specs/Plans: v2/docs/specs/*.md (BRD/PRDs/Plan).
  - Scope: FS and S3 providers with CRUD, streaming downloads (FS), S3 presigned/multipart uploads. ✨

  Repo Structure

  - v2/api: Node/TS API with providers, routes, env loading, error handling.
  - v2/web: Vue 3 app (File Browser, Upload tool, basic router).
  - v2/docs: BRD, PRDs for Providers, CRUD/Previews/Zip, Uploads, RBAC, Sharing.
  - v2/web/src: views/FileBrowser.vue, views/Upload.vue, lib/files.ts, lib/multipart.ts, lib/fsUpload.ts. 🗂️

  Run

  - API (dev): cd v2/api && pnpm i && pnpm dev → http://localhost:4000 🧪
  - Web (dev): cd v2/web && pnpm i && pnpm dev → http://localhost:5173 🖥️
  - Web expects API at same origin /v1 (proxy/serve together or configure accordingly). 🔗

  API Endpoints

  - Files:
      - GET /v1/files: list with provider, path, page, size, sort (name|size|modifiedAt), order. 📄
      - GET /v1/files/stat: single item metadata. 🧾
      - POST /v1/files: mkdir ({ provider, path }). 📁
      - PUT /v1/files/move: move ({ provider, src, dst }). 🚚
      - PUT /v1/files/copy: copy ({ provider, src, dst }). 📋
      - DELETE /v1/files: delete ({ provider, path }). 🗑️
      - POST /v1/files/zip: zip ({ provider, paths[], dst }). 🧩
      - POST /v1/files/unzip: unzip ({ provider, src, dst }). 📦
  - Streaming (FS only):
      - GET /v1/files/download?provider=fs&path=/... (Range/Etag supported). ⬇️
      - POST /v1/files/upload?provider=fs&path=/...&overwrite= (raw body). ⬆️
  - Presign (S3):
      - POST /v1/uploads/presign: PUT URL for direct upload. 🔏
      - POST /v1/downloads/presign: GET URL for download. 🔐
  - Multipart (S3):
      - POST /v1/uploads/multipart/init|presignPart|complete|abort. 🍱
  - Health:
      - GET /v1/health: {status: 'ok'}. ❤️

  Providers

  - Available: fs, s3 (registered via registerDefaultProviders() from env). 🧩
  - FS root: FS_ROOT (defaults to process.cwd()).
  - S3 env:
      - S3_BUCKET, S3_REGION, S3_ACCESS_KEY_ID, S3_SECRET_ACCESS_KEY
      - Optional: S3_ENDPOINT, S3_FORCE_PATH_STYLE=true, S3_ROOT_PREFIX
      - Optional URLs: AWS_S3_BUCKET_URL, AWS_S3_IPM_BUCKET_URL (referenced in config). 🧰
  - Safety: path normalization + root bounding (prevents traversal) in FS provider. 🔒

  Frontend (v2/web)

  - Views:
      - Home.vue: Health check + links. 🏠
      - FileBrowser.vue: List items (FS/S3 radio toggle), breadcrumbs, Up/Refresh, create folder, single file download (file), single/bulk delete, move (prompt), rename (prompt), basic table with
  modified date/size. 📂
      - Upload.vue: FS streaming upload (overwrite toggle, progress callback) and S3 multipart uploads. 📤
  - Data Layer:
      - lib/files.ts: calls /v1/files* CRUD, presign download.
      - lib/fsUpload.ts: FS streaming upload via /v1/files/upload.
      - lib/multipart.ts: S3 multipart flows via /v1/uploads/multipart/*. 📡

  What’s Implemented vs PRDs

  - PRD—Providers (FS, S3; SFTP/FTP planned):
      - Implemented: FS full (list/stat/read/write/move/copy/mkdir/delete/zip/unzip), S3 hooks (copy/move via provider impl, upload presign/multipart). ✅
      - Partial/Missing: SFTP/FTP providers not wired; provider registry scaffolding exists. ⚠️
  - PRD—CRUD/Search/Previews/Zip:
      - CRUD: List/stat/mkdir/move/copy/delete implemented; zip/unzip implemented. ✅
      - Search: No /v1/search endpoint yet; sorting exists server-side; client filters not present. ❌
      - Previews: No preview/image/pdf endpoints; FS download stream exists; S3 presigned GET present. ❌
      - Watermarking: Not implemented. ❌
  - PRD—Uploads:
      - FS stream upload: Implemented (/v1/files/upload). ✅
      - S3 multipart: Implemented (init, presignPart, complete, abort). ✅
      - Resumable/retry protocol (non-S3): Not implemented; FS uses simple stream. ❌
  - PRD—RBAC:
      - Roles/policies/ACL middleware: Not implemented; no auth in API/UI. ❌
      - UI permission guards: Not present. ❌
  - PRD—Sharing Links:
      - Not implemented (no share model/routes/UI). ❌

  Dev & Config

  - API:
      - pnpm dev runs on :4000 with CORS enabled for local dev.
      - Error handling unified via utils/errors.ts (Zod validation → 400, others → 500). 🧯
  - Web:
      - pnpm dev runs on :5173. Uses fetch to /v1/* (same origin proxy or adjust Vite config). 🔧

  Gaps & Next Steps (High-Impact)

  - Search: Add GET /v1/search with name/extension/mime/date filters and paginated results; wire a search box in the toolbar. 🔎
  - Previews: Add /v1/preview, thumbs pipeline (Sharp), PDF viewer; UI lightbox (image/pdf/text). 🖼️
  - RBAC: Introduce auth (JWT/cookies), roles, ACL middleware; hide/disable UI actions based on policy. 🛡️
  - Sharing: /v1/shares (create/revoke/public page), token/expiry/password, audit trail. 🔗
  - Soft Delete/Undo: Introduce .trash convention with undo in UI (move/restore), plus “Empty Trash”. 🗑️
  - Bulk Actions: Multi-select copy/move via destination picker (UI improvement; currently prompt). 📦
  - Theming/UX: Add theme/compact system (dropdown), toasts/notifications, keyboard shortcuts, loading states. 🎨
  - Config: Vite dev proxy for /v1 to :4000 to simplify local runs. 🔁

  Quick Start (Local)

  - API: cd v2/api && pnpm i && FS_ROOT=/your/root pnpm dev (and S3 env if needed). 🧪
  - Web: cd v2/web && pnpm i && pnpm dev → browse http://localhost:5173. 🖥️
  - FS Download: Click “Download” on files (FS). For S3, presigned download used via /v1/downloads/presign. ⬇️

  If you want, I can:

  - Add a themed, compact toolbar + toasts to v2/web.
  - Implement soft-delete (trash/undo) and inline rename on name click.
  - Draft /v1/search and a preview pipeline skeleton aligned with the PRDs. 💡