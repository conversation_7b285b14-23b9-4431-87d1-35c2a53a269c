# RBAC Implementation Plan

## Current State Analysis

### Existing Auth System
- **Authentication**: Google OAuth2 with JWT tokens
- **Authorization**: Basic policy system with 3 actions: `view`, `download`, `upload`
- **Admin System**: Simple `is_admin` flag for users
- **Policy Structure**: Subject-based (all/user/group) with path prefixes and allow/deny effects
- **Default Behavior**: View allowed by default, download/upload denied unless explicitly allowed

### Database Schema (Current)
```sql
users: sub, email, name, is_admin, blocked
groups: id, name
group_members: group_id, user_sub
sources: id, kind, name, config
policies: id, subject_type, subject_id, source_id, path_prefix, action, effect, expires_at
```

## PRD Requirements vs Current Gaps

### ✅ Already Implemented
- Basic authentication with JWT
- User management with admin flag
- Group system with membership
- Path-based ACLs with allow/deny
- Policy expiration support
- Admin UI for managing users, groups, policies

### ❌ Missing Features

#### 1. **Role System**
- **Gap**: No formal role system (admin, editor, viewer)
- **Current**: Only boolean `is_admin` flag
- **Required**: Proper role-based permissions with defaults

#### 2. **Extended Action Types**
- **Gap**: Only 3 actions (`view`, `download`, `upload`)
- **Required**: 10 actions (`list`, `stat`, `read`, `write`, `move`, `copy`, `delete`, `share`, `zip`, `search`)

#### 3. **Role-Based Default Permissions**
- **Gap**: Hard-coded default (view=allow, others=deny)
- **Required**: Role-specific defaults (admin=all, editor=CRUD, viewer=read-only)

#### 4. **Audit Logging**
- **Gap**: No audit trail for sensitive operations
- **Required**: Log share create/revoke, delete, privilege escalation

#### 5. **UI Guards**
- **Gap**: UI doesn't hide/disable forbidden actions
- **Required**: Dynamic UI based on user permissions

#### 6. **Policy Caching**
- **Gap**: No caching, direct DB queries for each check
- **Required**: O(1) decision via cached normalized rules

## Implementation Plan

### Phase 1: Core RBAC Foundation

#### Step 1.1: Expand Action Types
```typescript
// Update Action type in policy.ts
export type Action = 
  | 'list'     // Browse directory contents
  | 'stat'     // Get file/folder metadata
  | 'read'     // Read file contents
  | 'write'    // Create/modify files
  | 'move'     // Move/rename files
  | 'copy'     // Copy files
  | 'delete'   // Delete files/folders
  | 'share'    // Create/manage shares
  | 'zip'      // Create/extract archives
  | 'search'   // Search within sources
  | 'view'     // Legacy: maps to 'list' + 'stat'
  | 'download' // Legacy: maps to 'read'
  | 'upload'   // Legacy: maps to 'write'
```

#### Step 1.2: Add Role System
```sql
-- Add role column to users table
ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'viewer';
-- Possible values: 'admin', 'editor', 'viewer'
```

#### Step 1.3: Update Policy Engine
```typescript
// New role-based default permissions
const ROLE_DEFAULTS = {
  admin: ['list', 'stat', 'read', 'write', 'move', 'copy', 'delete', 'share', 'zip', 'search'],
  editor: ['list', 'stat', 'read', 'write', 'move', 'copy', 'zip', 'search'],
  viewer: ['list', 'stat', 'read']
}

// Update isAllowed function to check role defaults
export function isAllowed(sub: string, sourceId: number, path: string, action: Action) {
  if (userIsAdmin(sub)) return true
  
  const groups = userGroups(sub)
  const pols = policiesFor(sub, groups, sourceId, path, action)
  
  // Explicit deny always wins
  const hasDeny = pols.some(p => p.effect === 'deny')
  if (hasDeny) return false
  
  // Explicit allow wins over defaults
  const hasAllow = pols.some(p => p.effect === 'allow')
  if (hasAllow) return true
  
  // Check role-based defaults
  const userRole = getUserRole(sub)
  const roleDefaults = ROLE_DEFAULTS[userRole] || ROLE_DEFAULTS.viewer
  return roleDefaults.includes(action)
}
```

### Phase 2: Enhanced Features

#### Step 2.1: Audit Logging
```sql
CREATE TABLE audit_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_sub TEXT NOT NULL,
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT,
  details TEXT,
  ip_address TEXT,
  user_agent TEXT,
  created_at TEXT NOT NULL
);
```

```typescript
// Audit sensitive operations
function auditLog(user: User, action: string, resourceType: string, resourceId?: string, details?: any) {
  db.prepare(`
    INSERT INTO audit_logs (user_sub, action, resource_type, resource_id, details, created_at)
    VALUES (?, ?, ?, ?, ?, ?)
  `).run(user.sub, action, resourceType, resourceId, JSON.stringify(details), new Date().toISOString())
}
```

#### Step 2.2: Policy Caching
```typescript
// In-memory policy cache with TTL
class PolicyCache {
  private cache = new Map<string, { result: boolean, expires: number }>()
  private ttl = 5 * 60 * 1000 // 5 minutes
  
  get(key: string): boolean | null {
    const entry = this.cache.get(key)
    if (!entry || entry.expires < Date.now()) {
      this.cache.delete(key)
      return null
    }
    return entry.result
  }
  
  set(key: string, result: boolean) {
    this.cache.set(key, { result, expires: Date.now() + this.ttl })
  }
  
  invalidate(pattern?: string) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) this.cache.delete(key)
      }
    } else {
      this.cache.clear()
    }
  }
}
```

### Phase 3: UI Integration

#### Step 3.1: Update PolicyModal
- Add all new action types to dropdown
- Add role selection for users
- Show role-based default permissions

#### Step 3.2: Implement UI Guards
```typescript
// Composable for permission checking
export function usePermissions() {
  const user = getCurrentUser()
  
  function canPerform(action: Action, sourceId: number, path: string = '/') {
    return isAllowed(user.sub, sourceId, path, action)
  }
  
  function getAvailableActions(sourceId: number, path: string = '/') {
    return ALL_ACTIONS.filter(action => canPerform(action, sourceId, path))
  }
  
  return { canPerform, getAvailableActions }
}
```

#### Step 3.3: Dynamic UI Components
```vue
<!-- Example: Conditional action buttons -->
<template>
  <div class="file-actions">
    <button v-if="canPerform('read', sourceId, filePath)" @click="download">
      Download
    </button>
    <button v-if="canPerform('delete', sourceId, filePath)" @click="deleteFile">
      Delete
    </button>
    <button v-if="canPerform('share', sourceId, filePath)" @click="createShare">
      Share
    </button>
  </div>
</template>
```

### Phase 4: Migration & Testing

#### Step 4.1: Data Migration
```typescript
// Migrate existing users to role system
function migrateUsersToRoles() {
  const users = db.prepare('SELECT sub, is_admin FROM users').all()
  for (const user of users) {
    const role = user.is_admin ? 'admin' : 'viewer'
    db.prepare('UPDATE users SET role = ? WHERE sub = ?').run(role, user.sub)
  }
}

// Migrate legacy action types in policies
function migrateLegacyActions() {
  const actionMap = {
    'view': ['list', 'stat'],
    'download': ['read'],
    'upload': ['write']
  }
  
  for (const [oldAction, newActions] of Object.entries(actionMap)) {
    const policies = db.prepare('SELECT * FROM policies WHERE action = ?').all(oldAction)
    for (const policy of policies) {
      // Create new policies for each mapped action
      for (const newAction of newActions) {
        db.prepare(`
          INSERT INTO policies (subject_type, subject_id, source_id, path_prefix, action, effect, expires_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `).run(
          policy.subject_type, policy.subject_id, policy.source_id,
          policy.path_prefix, newAction, policy.effect, policy.expires_at
        )
      }
    }
    // Remove old policy
    db.prepare('DELETE FROM policies WHERE action = ?').run(oldAction)
  }
}
```

#### Step 4.2: Testing Strategy
- Unit tests for policy engine with all action types
- Integration tests for role-based permissions
- E2E tests for UI guards and admin interfaces
- Performance tests for policy caching
- Security tests for privilege escalation prevention

## Implementation Priority

### High Priority (Phase 1)
1. ✅ Expand Action types
2. ✅ Add Role system to database
3. ✅ Update policy engine with role defaults
4. ✅ Update PolicyModal UI

### Medium Priority (Phase 2)
1. ✅ Implement audit logging
2. ✅ Add policy caching
3. ✅ Update admin interfaces

### Lower Priority (Phase 3)
1. ✅ Implement UI guards
2. ✅ Add comprehensive testing
3. ✅ Performance optimization

## Success Criteria

- ✅ All PRD requirements implemented
- ✅ Backward compatibility maintained
- ✅ Policy cache hit rate >95% under load
- ✅ UI dynamically reflects user permissions
- ✅ Audit trail for all sensitive operations
- ✅ E2E tests verify UI and API alignment
- ✅ Zero privilege escalation vulnerabilities

## Risk Mitigation

1. **Breaking Changes**: Implement feature flags for gradual rollout
2. **Performance**: Implement caching before expanding to all action types
3. **Security**: Thorough testing of permission boundaries
4. **Migration**: Backup database before schema changes
5. **Rollback**: Keep legacy action support during transition period