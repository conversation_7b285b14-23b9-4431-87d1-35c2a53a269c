# Authentication Redirect Loop Issue - Postmortem

## Issue Summary
Users were unable to log in to the application at https://files.agrizy.in. After successful Google OAuth authentication, they were immediately redirected back to the login page, creating an infinite loop.

## Timeline
1. **Initial Report**: User reported being "forced out to login screen again" after OAuth
2. **First Investigation**: Discovered Nginx misconfiguration
3. **Second Issue**: API returning empty responses despite 200 status
4. **Root Cause Found**: Frontend race condition between token storage and navigation
5. **Resolution**: Changed navigation method to prevent race condition

## Root Causes Identified

### 1. Nginx Configuration Issue
**Problem**: The Nginx configuration for files.agrizy.in was incorrectly proxying to port 9900 (development server) instead of serving static production files.

```nginx
# INCORRECT - was proxying to dev server
location / {
    proxy_pass http://localhost:9900;
}
```

**Fix**: Changed to serve static files with Vue Router SPA support:
```nginx
server {
    server_name files.agrizy.in;
    root /home/<USER>/apps/file-browser/web/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 2. API Empty Response Bodies
**Problem**: The Fastify backend was returning 200 status codes but with empty response bodies for successful authentication.

**Location**: `/api/dist/routes/auth.js`

**Cause**: Using plain `return` statements instead of Fastify's `reply.send()`:
```javascript
// INCORRECT - returns empty response
return { token, user: { ...user, roles, is_admin: roles.includes('admin') } }
```

**Fix**: Changed to use proper Fastify response method:
```javascript
// CORRECT - sends response body
return reply.send({ token, user: { ...user, roles, is_admin: roles.includes('admin') } })
```

**Affected Endpoints**:
- `/v1/auth/google` (line 70)
- `/v1/auth/me` (line 88)
- `/v1/auth/user-actions` (line 112)

### 3. Frontend Race Condition
**Problem**: Even after fixing the API responses, the browser still redirected to login due to a race condition.

**Location**: `/web/src/views/Login.vue`

**Cause**: Using `window.location.href = '/'` for navigation caused a full page reload:
```javascript
// INCORRECT - causes race condition
await loginWithGoogleIdToken(idToken)
window.location.href = '/'  // Full page reload
```

**Race Condition Sequence**:
1. Token stored in localStorage (async operation)
2. Page immediately reloads with `window.location.href`
3. Router guard checks for token before localStorage syncs
4. Token not found → redirect to login
5. Infinite loop

**Fix**: Use Vue Router's SPA navigation:
```javascript
// CORRECT - no page reload, maintains SPA context
import { useRouter } from 'vue-router'
const router = useRouter()

await loginWithGoogleIdToken(idToken)
await router.push('/')  // SPA navigation
```

## Additional Issues Encountered

### 4. File Deployment Corruption
**Problem**: During deployment, the auth.js file became corrupted when transferred via SSM with base64 encoding.

**Error**: `SyntaxError: The requested module './routes/auth.js' does not provide an export named 'registerAuthRoutes'`

**Resolution**: Manual file copy by user resolved the corruption issue.

### 5. Nginx Permission Issues
**Problem**: Nginx couldn't access the web directory due to permission issues.

**Error**: `stat() failed (13: Permission denied)`

**Fix**: Set proper directory permissions:
```bash
chmod 755 /home/<USER>/apps/file-browser/web
chmod 755 /home/<USER>/apps/file-browser/web/dist
```

## Debugging Evidence

### API Response Testing
```bash
# Empty response despite 200 status
curl -X POST https://files-api.agrizy.in/v1/auth/google \
  -H "Content-Type: application/json" \
  -d '{"idToken": "..."}'
# Response: (empty)

# After fix - proper response
curl -X POST https://files-api.agrizy.in/v1/auth/google \
  -H "Content-Type: application/json" \
  -d '{"idToken": "..."}'
# Response: {"token":"eyJ...","user":{...}}
```

### Browser Console Evidence
The browser console showed successful authentication but immediate navigation away:
- Google OAuth popup completed successfully
- API returned 200 status with token
- Page immediately reloaded before token was accessible
- Router guard found no token and redirected to login

## Lessons Learned

1. **Fastify Response Handling**: Always use `reply.send()` for response bodies in Fastify, not plain `return` statements.

2. **SPA Navigation**: In Vue applications, always use Vue Router for navigation (`router.push()`) rather than `window.location` to maintain SPA context and avoid race conditions.

3. **Race Conditions**: Be aware of timing issues between asynchronous operations (localStorage) and synchronous checks (router guards).

4. **Nginx Configuration**: Ensure production deployments serve static files correctly with proper SPA routing support, not proxy to development servers.

5. **Deployment Verification**: Always verify file integrity after deployment, especially when using encoding/transfer methods.

## Prevention Measures

1. **Code Reviews**: Ensure Fastify route handlers always use proper response methods
2. **Testing**: Include E2E tests for authentication flows
3. **Deployment**: Use git pull/push for deployments rather than file transfers
4. **Monitoring**: Add logging for authentication flows to catch issues early
5. **Documentation**: Document the correct patterns for both backend responses and frontend navigation

## Final Solution

The complete fix required three changes:
1. Nginx configuration to serve static files with SPA support
2. Fastify routes to use `reply.send()` for responses
3. Vue login component to use `router.push()` instead of `window.location.href`

All three issues needed to be resolved for authentication to work properly.